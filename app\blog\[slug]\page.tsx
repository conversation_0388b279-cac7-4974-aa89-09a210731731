import { getBlogPosts, getPostDetail } from "@/utils/wordpress";
import { notFound } from "next/navigation";
import Container from "@/components/container";
import type { Metadata } from "next";
import BreadcrumbUI from "@/components/breadcrumb";
import ShareButtons from "@/components/sharebutton";
import Script from "next/script";

interface BlogDetailPageProps {
  params: { slug: string };
}

const BASE_URL = "https://global-mmo.com";

export async function generateStaticParams() {
  const articles = await getBlogPosts();
  return articles.map((article: any) => ({ slug: article.slug }));
}

export async function generateMetadata({ params }: BlogDetailPageProps): Promise<Metadata> {
  const post = await getPostDetail(params.slug);

  if (!post) return { title: "Bài viết không tồn tại" };

  const title = post.title?.rendered || "Bài viết | Global MMO";
  const description = post.excerpt?.rendered;
  const image = post._embedded?.["wp:featuredmedia"]?.[0]?.source_url || "/logo.webp";
  const url = `${BASE_URL}/blog/${post.slug}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      type: "article",
      images: [
        {
          url: image,
          width: 800,
          height: 600,
          alt: "Ảnh đại diện bài viết",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image],
    },
  };
}

export default async function BlogDetailPage({ params }: BlogDetailPageProps) {
  const post = await getPostDetail(params.slug);

  if (!post) return notFound();

  const author = post._embedded?.author?.[0]?.name || "Global MMO";
  const publishedDate = new Date(post.date).toLocaleDateString("vi-VN");
  const image = post._embedded?.["wp:featuredmedia"]?.[0]?.source_url || "/logo.webp";
  const shareUrl = `${BASE_URL}/blog/${post.slug}`;

  return (
    <main className="py-12">
      <Container>
        <article className="max-w-3xl mx-auto">
          <BreadcrumbUI nameSecond="Danh sách bài viết" linkSecond="/blog" nameFourth={post.title.rendered} />
          <h1
            className="text-3xl md:text-4xl font-bold my-4"
            dangerouslySetInnerHTML={{ __html: post.title.rendered }}
          />
          <p className=" mb-6 text-sm">
            Đăng bởi {author} - {publishedDate}
          </p>
          <div
            className="prose max-w-3xl prose-img:max-w-3xl overflow-hidden"
            dangerouslySetInnerHTML={{ __html: post.content.rendered }}
          />
          <ShareButtons shareUrl={shareUrl} />
        </article>
      </Container>

      <Script id="blog-detail-post-schema" type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "BlogPosting",
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": shareUrl,
          },
          "headline": post.title?.rendered || "Global MMO",
          "description": post.excerpt || "Bài viết game mới nhất từ Global MMO",
          "image": image
            ? [
                {
                  "@type": "ImageObject",
                  "url": image,
                  "width": 800,
                  "height": 600,
                  "alt": post.title || "Global MMO",
                },
              ]
            : [],
          "author": {
            "@type": "Person",
            "name": "Global MMO",
            "url": BASE_URL,
          },
          "publisher": {
            "@type": "Organization",
            "name": "Global MMO",
            "url": BASE_URL,
            "logo": {
              "@type": "ImageObject",
              "url": `${BASE_URL}/logo.webp`,
              "width": 120,
              "height": 120,
            },
          },
          "url": shareUrl,
          "datePublished": post.date ? new Date(post.date).toISOString() : new Date().toISOString(),
          "dateModified": post.date
            ? new Date(post.date).toISOString()
            : post.date
            ? new Date(post.date).toISOString()
            : new Date().toISOString(),
          "isAccessibleForFree": true,
          "inLanguage": "vi",
          "articleSection": "MMO, Kiếm tiền online",
          "articleBody": post.content?.rendered?.replace(/<[^>]+>/g, "").split(/\s+/).slice(0, 500).join(" ") || "",
          "wordCount": post.content?.rendered?.split(/\s+/).length || 0,
        })}
      </Script>

      <Script id="breadcrumb-jsonld" type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": [
            { "@type": "ListItem", "position": 1, "name": "Trang chủ", "item": `${BASE_URL}` },
            { "@type": "ListItem", "position": 2, "name": "Bài viết", "item": `${BASE_URL}/blog` },
            { "@type": "ListItem", "position": 3, "name": post.title?.rendered || "", "item": shareUrl },
          ],
        })}
      </Script>
    </main>
  );
}
