import type { Metadata } from "next"
import Script from "next/script";
import PostGrid from "@/components/blog/post-grid"
import Container from "@/components/container"
import { getBlogPosts } from "@/utils/wordpress";

export const metadata: Metadata = {
  title: "Bài viết - Dịch vụ tâm lý Anmy",
  description: "Tổng hợp kiến thức, bài viết và thông tin hữu ích về sức khỏe tinh thần, tâm lý học và phát triển bản thân từ Anmy Psychology.",
}

export default async function BlogPage() {
  
  const posts = await getBlogPosts(12);
  
  console.log("posts", posts);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "<PERSON>h sách bài viết | Anmy Psychology",
    description:
      "Tổng hợp kiến thức, bài viết và thông tin hữu ích về sức khỏe tinh thần, tâ<PERSON> lý học và phát triển bản thân từ Anmy Psychology.",
    url: `https://anmy.com/blog`,
    image:
      "https://anmy.com/logo.webp",
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Trang chủ",
          item: "https://anmy.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Danh sách bài viết",
          item: "https://anmy.com/blog",
        },
      ],
    },
    hasPart: posts.map((post) => ({
      "@type": "BlogPosting",
      headline: post.title,
      description: post.excerpt,
      image: post.coverImage,
      author: {
        "@type": "Person",
        name: "Anmy Psychology",
      },
      datePublished: post.publishedAt,
      dateModified: post.createdAt,
      url: `https://anmy.com/blog/${post.slug}`,
    })),
  };
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col bg-white">
      <main className="flex-1">
        <section className="w-full py-12 bg-white">
          <Container>
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-black">Bài viết của chúng tôi</h1>
              <p className="max-w-[700px] text-lg md:text-xl text-gray-700">
                Những kiến thức, mẹo hay và góc nhìn mới nhất về tâm lý học từ đội ngũ Anmy Psychology
              </p>
            </div>

            {posts.length > 0 ? (
              <PostGrid posts={posts} showViewAll={false} />
            ) : (
              <div className="text-center py-20">
                <p className="text-gray-700">Chưa có bài viết nào. Vui lòng quay lại sau!</p>
              </div>
            )}
          </Container>
        </section>
      </main>
      <Script
        type="application/ld+json"
        id="blog-page-jsonld"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
    </div>
  );
}