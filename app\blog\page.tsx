import type { Metadata } from "next"
import Script from "next/script";
import PostGrid from "@/components/blog/post-grid"
import Container from "@/components/container"
import { getBlogPosts } from "@/utils/wordpress";

export const metadata: Metadata = {
  title: "Bài viết - Dịch vụ tâm lý Anmy",
  description: "Tổng hợp kiến thức, bài viết và thông tin hữu ích về sức khỏe tinh thần, tâm lý học và phát triển bản thân từ Anmy Psychology.",
}

export default async function BlogPage() {
  
  const posts = await getBlogPosts(12);
  
  console.log("posts", posts);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "<PERSON>h sách bài viết | Anmy Psychology",
    description:
      "Tổng hợp kiến thức, bài viết và thông tin hữu ích về sức khỏe tinh thần, tâ<PERSON> lý học và phát triển bản thân từ Anmy Psychology.",
    url: `https://anmy.com/blog`,
    image:
      "https://anmy.com/logo.webp",
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Trang chủ",
          item: "https://anmy.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Danh sách bài viết",
          item: "https://anmy.com/blog",
        },
      ],
    },
    hasPart: posts.map((post) => ({
      "@type": "BlogPosting",
      headline: post.title,
      description: post.excerpt,
      image: post.coverImage,
      author: {
        "@type": "Person",
        name: "Anmy Psychology",
      },
      datePublished: post.publishedAt,
      dateModified: post.createdAt,
      url: `https://anmy.com/blog/${post.slug}`,
    })),
  };
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-24 md:py-32 overflow-hidden bg-white">
          {/* Background with animated gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#375E5E]/5 to-transparent animate-pulse"></div>

          {/* Floating elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-[#375E5E] rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-[#375E5E] rounded-full opacity-15 animate-bounce delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-[#375E5E] rounded-full opacity-20 animate-bounce delay-500"></div>

          <Container className="relative z-10">
            <div className="flex flex-col items-center justify-center space-y-8 text-center">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                  Kiến thức tâm lý
                </div>
                <h1 className="text-5xl md:text-7xl font-bold tracking-tight text-[#375E5E]">
                  Bài viết
                </h1>
                <p className="text-xl md:text-2xl font-medium text-black">
                  Kiến thức và góc nhìn chuyên sâu
                </p>
              </div>

              <p className="max-w-[700px] text-lg md:text-xl text-gray-700 leading-relaxed">
                Những kiến thức, mẹo hay và góc nhìn mới nhất về tâm lý học từ đội ngũ chuyên gia Anmy Psychology
              </p>

              {/* Stats */}
              <div className="flex flex-wrap justify-center gap-8 mt-12 pt-8 border-t border-gray-200">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-[#375E5E]">{posts.length}+</div>
                  <div className="text-sm text-gray-600">Bài viết chuyên sâu</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-[#375E5E]">100%</div>
                  <div className="text-sm text-gray-600">Nội dung chuyên nghiệp</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-[#375E5E]">Miễn phí</div>
                  <div className="text-sm text-gray-600">Kiến thức hữu ích</div>
                </div>
              </div>
            </div>
          </Container>
        </section>

        {/* Blog Posts Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                Bài viết mới nhất
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 text-black">
                Khám phá kiến thức tâm lý
              </h2>
              <p className="text-xl text-gray-700 max-w-[700px] mx-auto">
                Cập nhật những thông tin và kiến thức mới nhất về sức khỏe tinh thần và phát triển bản thân
              </p>
            </div>

            {posts.length > 0 ? (
              <PostGrid posts={posts} showViewAll={false} />
            ) : (
              <div className="text-center py-20">
                <div className="max-w-md mx-auto">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-2xl bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-12 h-12 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-black mb-2">Đang cập nhật nội dung</h3>
                  <p className="text-gray-700">Chúng tôi đang chuẩn bị những bài viết chất lượng. Vui lòng quay lại sau!</p>
                </div>
              </div>
            )}
          </Container>
        </section>
      </main>
      <Script
        type="application/ld+json"
        id="blog-page-jsonld"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
    </div>
  );
}