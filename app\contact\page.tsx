import type { Metadata } from "next"
import Container from "@/components/container"
import ContactForm from "@/components/contact/contact-form"

export const metadata: Metadata = {
  title: "Liên hệ - Anmy Psychology",
  description: "Liên hệ với đội ngũ tâm lý Anmy. Chúng mình luôn sẵn sàng lắng nghe và hỗ trợ bạn 24/7. Đặt lịch tư vấn hoặc chat ngay!",
  keywords: ["liên hệ", "tư vấn tâm lý", "đặt lịch", "anmy psychology", "hỗ trợ tâm lý"],
}

export default function ContactPage() {
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-20 overflow-hidden bg-white">
          {/* Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>

          {/* Floating elements */}
          <div className="absolute top-10 left-10 w-16 h-16 bg-[#375E5E] rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-32 right-20 w-12 h-12 bg-[#375E5E] rounded-full opacity-15 animate-bounce delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-10 h-10 bg-[#375E5E] rounded-full opacity-20 animate-bounce delay-500"></div>

          <Container className="relative z-10">
            <div className="flex flex-col items-center justify-center space-y-8 text-center">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                  Liên hệ với chúng tôi
                </div>

                <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
                  <span className="text-[#375E5E]">
                    Liên hệ
                  </span>
                  <br />
                  <span className="text-2xl md:text-3xl font-medium text-black">
                    Chúng tôi sẵn sàng hỗ trợ bạn
                  </span>
                </h1>
              </div>

              <p className="max-w-[700px] text-lg md:text-xl text-gray-700 leading-relaxed">
                Hãy liên hệ với chúng tôi để được tư vấn và hỗ trợ chuyên nghiệp.
                Mọi thông tin đều được bảo mật tuyệt đối và chúng tôi sẽ phản hồi trong vòng 24 giờ.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>Bảo mật tuyệt đối</span>
                  <span>•</span>
                  <span>Phản hồi nhanh chóng</span>
                  <span>•</span>
                  <span>Dịch vụ chuyên nghiệp</span>
                </div>
              </div>
            </div>
          </Container>
        </section>

        {/* Contact Form Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <ContactForm />
          </Container>
        </section>

        {/* Contact Info Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-black">
                  Các kênh liên hệ
                </h2>
                <p className="text-xl text-gray-700">
                  Chọn phương thức liên hệ phù hợp với nhu cầu của bạn
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {/* Emergency Support */}
                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">Hỗ trợ khẩn cấp</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Nếu bạn đang trong tình huống khẩn cấp, hãy gọi ngay cho chúng tôi!
                  </p>
                  <div className="space-y-2 text-sm">
                    <p className="font-semibold text-[#375E5E]">Hotline 24/7</p>
                    <p className="text-gray-600">1900-xxxx (miễn phí)</p>
                  </div>
                </div>

                {/* Chat Support */}
                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">Chat trực tuyến</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Chat với chúng tôi qua các platform mà bạn quen thuộc!
                  </p>
                  <div className="space-y-2 text-sm">
                    <p className="font-semibold text-[#375E5E]">Zalo, Messenger</p>
                    <p className="text-gray-600">Phản hồi trong 30 phút</p>
                  </div>
                </div>

                {/* Email Support */}
                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">Email hỗ trợ</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Gửi email chi tiết cho chúng tôi để được tư vấn kỹ hơn.
                  </p>
                  <div className="space-y-2 text-sm">
                    <p className="font-semibold text-[#375E5E]"><EMAIL></p>
                    <p className="text-gray-600">Phản hồi trong 24h</p>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </section>

        {/* FAQ Quick Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-8 text-[#375E5E]">
                Câu hỏi thường gặp
              </h2>

              <div className="grid gap-6 md:grid-cols-2 text-left">
                <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-[#375E5E] transition-colors duration-300">
                  <h3 className="font-bold mb-2 text-[#375E5E]">Chi phí tư vấn là bao nhiêu?</h3>
                  <p className="text-sm text-gray-700">Buổi tư vấn đầu tiên hoàn toàn miễn phí! Các buổi tiếp theo có mức phí hợp lý cho sinh viên.</p>
                </div>

                <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-[#375E5E] transition-colors duration-300">
                  <h3 className="font-bold mb-2 text-[#375E5E]">Lịch làm việc như thế nào?</h3>
                  <p className="text-sm text-gray-700">Chúng tôi linh hoạt theo lịch của bạn, kể cả cuối tuần và buổi tối.</p>
                </div>

                <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-[#375E5E] transition-colors duration-300">
                  <h3 className="font-bold mb-2 text-[#375E5E]">Thông tin có được bảo mật không?</h3>
                  <p className="text-sm text-gray-700">Tuyệt đối bảo mật! Chúng tôi tuân thủ nghiêm ngặt quy định bảo mật thông tin khách hàng.</p>
                </div>

                <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-[#375E5E] transition-colors duration-300">
                  <h3 className="font-bold mb-2 text-[#375E5E]">Có tư vấn online không?</h3>
                  <p className="text-sm text-gray-700">Có! Chúng tôi hỗ trợ tư vấn qua video call, rất tiện cho bạn ở xa.</p>
                </div>
              </div>

              <div className="mt-12">
                <a
                  href="/faq"
                  className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  Xem thêm câu hỏi
                </a>
              </div>
            </div>
          </Container>
        </section>
      </main>
    </div>
  )
}
