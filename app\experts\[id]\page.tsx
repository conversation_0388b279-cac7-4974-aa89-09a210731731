import type { Metadata } from "next"
import { notFound } from "next/navigation"
import Container from "@/components/container"
import Link from "next/link"

// Mock data - trong thực tế sẽ lấy từ database
const experts = [
  {
    id: 1,
    name: "ThS. Nguyễ<PERSON>",
    title: "Chuyên gia tâm lý lâm sàng",
    specialties: ["Trầm cảm", "Lo âu", "Stress", "Tâm lý trẻ em"],
    experience: "8+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Quốc gia Hà Nội",
    description: "<PERSON>yên sâu về tâm lý lâm sàng với nhiều năm kinh nghiệm điều trị các rối loạn tâm lý phổ biến.",
    detailedDescription: `
      ThS. <PERSON><PERSON>ễ<PERSON>h là một chuyên gia tâm lý lâm sàng với hơn 8 năm kinh nghiệm trong lĩnh vực tư vấn và điều trị tâm lý. 
      <PERSON><PERSON> đã giúp đỡ hàng trăm khách hàng vượt qua những khó khăn về tâm lý, từ các vấn đề thường gặp như stress, lo âu đến những rối loạn phức tạp hơn.

      Với phương pháp tiếp cận nhân văn và khoa học, ThS. Minh Anh luôn tạo ra một môi trường an toàn và thoải mái cho khách hàng. 
      Cô tin rằng mỗi người đều có khả năng tự chữa lành và phát triển, và vai trò của chuyên gia là đồng hành và hướng dẫn trong hành trình đó.
    `,
    image: "/images/expert-1.jpg",
    languages: ["Tiếng Việt", "English"],
    consultationTypes: ["Trực tiếp", "Online", "Điện thoại"],
    rating: 4.9,
    reviewCount: 127,
    isAvailable: true,
    workingHours: "Thứ 2-6: 8:00-17:00, Thứ 7: 8:00-12:00",
    certifications: [
      "Chứng chỉ Tâm lý lâm sàng - Bộ Y tế",
      "Chứng chỉ CBT (Cognitive Behavioral Therapy)",
      "Chứng chỉ Tư vấn tâm lý trẻ em"
    ],
    achievements: [
      "Giải thưởng Chuyên gia tâm lý xuất sắc 2023",
      "Tác giả 15+ bài nghiên cứu về tâm lý lâm sàng",
      "Diễn giả tại 20+ hội thảo chuyên môn"
    ],
    approaches: [
      "Liệu pháp nhận thức hành vi (CBT)",
      "Liệu pháp tâm lý động lực",
      "Mindfulness và thiền định",
      "Liệu pháp gia đình hệ thống"
    ]
  },
  {
    id: 2,
    name: "BS. Trần Thị Hương",
    title: "Bác sĩ tâm thần",
    specialties: ["Rối loạn tâm thần", "Bipolar", "Tâm lý học đường"],
    experience: "12+ năm kinh nghiệm",
    education: "Bác sĩ Đa khoa - ĐH Y Hà Nội",
    description: "Bác sĩ chuyên khoa tâm thần với kinh nghiệm phong phú trong điều trị các rối loạn tâm lý phức tạp.",
    detailedDescription: `
      BS. Trần Thị Hương là bác sĩ chuyên khoa tâm thần với hơn 12 năm kinh nghiệm trong lĩnh vực chẩn đoán và điều trị các rối loạn tâm thần.
      Bà đã từng công tác tại các bệnh viện lớn và có nhiều kinh nghiệm trong việc điều trị các ca bệnh phức tạp.

      Với chuyên môn sâu về các rối loạn tâm thần như rối loạn lưỡng cực, trầm cảm nặng, và các vấn đề tâm lý học đường,
      BS. Hương luôn áp dụng phương pháp điều trị toàn diện, kết hợp giữa thuốc và liệu pháp tâm lý.
    `,
    image: "/images/expert-2.jpg",
    languages: ["Tiếng Việt"],
    consultationTypes: ["Trực tiếp", "Online"],
    rating: 4.8,
    reviewCount: 89,
    isAvailable: false,
    workingHours: "Thứ 2-6: 9:00-16:00",
    certifications: [
      "Bằng chuyên khoa I Tâm thần - Bộ Y tế",
      "Chứng chỉ điều trị Bipolar",
      "Chứng chỉ tâm lý học đường"
    ],
    achievements: [
      "Bác sĩ xuất sắc năm 2022",
      "Tác giả 25+ công trình nghiên cứu",
      "Chuyên gia tư vấn cho 5+ trường học"
    ],
    approaches: [
      "Điều trị bằng thuốc chuyên khoa",
      "Liệu pháp tâm lý hỗ trợ",
      "Tư vấn gia đình",
      "Theo dõi dài hạn"
    ]
  },
  {
    id: 3,
    name: "ThS. Lê Văn Đức",
    title: "Chuyên gia tâm lý gia đình",
    specialties: ["Tâm lý gia đình", "Tâm lý hôn nhân", "Tư vấn cặp đôi"],
    experience: "6+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Sư phạm TP.HCM",
    description: "Chuyên gia tư vấn tâm lý gia đình và hôn nhân, giúp xây dựng mối quan hệ hạnh phúc.",
    detailedDescription: `
      ThS. Lê Văn Đức chuyên sâu về tâm lý gia đình và các mối quan hệ. Với 6 năm kinh nghiệm,
      anh đã giúp đỡ hàng trăm cặp đôi và gia đình vượt qua những khó khăn trong mối quan hệ.

      Phương pháp của ThS. Đức tập trung vào việc xây dựng sự hiểu biết và giao tiếp hiệu quả giữa các thành viên trong gia đình.
      Anh tin rằng mọi mối quan hệ đều có thể được cải thiện thông qua sự thấu hiểu và nỗ lực chung.
    `,
    image: "/images/expert-3.jpg",
    languages: ["Tiếng Việt", "English"],
    consultationTypes: ["Trực tiếp", "Online"],
    rating: 4.7,
    reviewCount: 64,
    isAvailable: true,
    workingHours: "Thứ 2-7: 8:00-20:00",
    certifications: [
      "Chứng chỉ tư vấn gia đình - Hiệp hội Tâm lý VN",
      "Chứng chỉ tư vấn hôn nhân",
      "Chứng chỉ Gottman Method"
    ],
    achievements: [
      "Chuyên gia tư vấn gia đình được yêu thích 2023",
      "Tác giả sách 'Hạnh phúc gia đình'",
      "Diễn giả 30+ workshop về gia đình"
    ],
    approaches: [
      "Gottman Method",
      "Liệu pháp gia đình hệ thống",
      "Kỹ thuật giao tiếp hiệu quả",
      "Xây dựng lòng tin"
    ]
  }
]

interface PageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const expert = experts.find(e => e.id === parseInt(params.id))
  
  if (!expert) {
    return {
      title: "Không tìm thấy chuyên gia - Anmy Psychology"
    }
  }

  return {
    title: `${expert.name} - ${expert.title} | Anmy Psychology`,
    description: expert.description,
    keywords: [expert.name, expert.title, ...expert.specialties, "anmy psychology"].join(", ")
  }
}

export default function ExpertDetailPage({ params }: PageProps) {
  const expert = experts.find(e => e.id === parseInt(params.id))

  if (!expert) {
    notFound()
  }

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-20 overflow-hidden bg-white">
          {/* Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>
          
          <Container className="relative z-10">
            {/* Breadcrumb */}
            <nav className="mb-8">
              <ol className="flex items-center space-x-2 text-sm text-gray-600">
                <li><Link href="/" className="hover:text-[#375E5E]">Trang chủ</Link></li>
                <li><span>/</span></li>
                <li><Link href="/experts" className="hover:text-[#375E5E]">Chuyên gia</Link></li>
                <li><span>/</span></li>
                <li className="text-[#375E5E] font-medium">{expert.name}</li>
              </ol>
            </nav>

            <div className="grid gap-12 lg:grid-cols-3">
              {/* Expert Info */}
              <div className="lg:col-span-2">
                <div className="flex items-start gap-6 mb-8">
                  {/* Avatar */}
                  <div className="relative w-32 h-32 rounded-3xl overflow-hidden bg-gray-100 flex-shrink-0">
                    <div className="w-full h-full bg-gradient-to-br from-[#375E5E]/20 to-[#375E5E]/40 flex items-center justify-center">
                      <svg className="w-16 h-16 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>

                  {/* Basic Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-3xl md:text-4xl font-bold text-black">{expert.name}</h1>
                      {expert.isAvailable && (
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-green-600 font-medium">Đang hoạt động</span>
                        </div>
                      )}
                    </div>
                    <p className="text-xl text-[#375E5E] font-semibold mb-2">{expert.title}</p>
                    <p className="text-gray-600 mb-4">{expert.experience}</p>
                    
                    {/* Rating */}
                    <div className="flex items-center gap-2 mb-4">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-5 h-5 ${
                              i < Math.floor(expert.rating) ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-lg font-semibold text-black">{expert.rating}</span>
                      <span className="text-gray-600">({expert.reviewCount} đánh giá)</span>
                    </div>

                    {/* Specialties */}
                    <div className="flex flex-wrap gap-2">
                      {expert.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 text-sm font-medium bg-[#375E5E]/10 text-[#375E5E] rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-black mb-4">Giới thiệu</h2>
                  <div className="prose prose-gray max-w-none">
                    {expert.detailedDescription.split('\n').map((paragraph, index) => (
                      paragraph.trim() && (
                        <p key={index} className="text-gray-700 leading-relaxed mb-4">
                          {paragraph.trim()}
                        </p>
                      )
                    ))}
                  </div>
                </div>

                {/* Approaches */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-black mb-4">Phương pháp điều trị</h2>
                  <div className="grid gap-3 md:grid-cols-2">
                    {expert.approaches.map((approach, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                        <div className="w-2 h-2 bg-[#375E5E] rounded-full"></div>
                        <span className="text-gray-700">{approach}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-black mb-4">Thành tích nổi bật</h2>
                  <div className="space-y-3">
                    {expert.achievements.map((achievement, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-[#375E5E]/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <svg className="w-3 h-3 text-[#375E5E]" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-gray-700">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-8 space-y-6">
                  {/* Contact Card */}
                  <div className="p-6 bg-white border border-gray-200 rounded-3xl shadow-lg">
                    <h3 className="text-xl font-bold text-black mb-4">Đặt lịch tư vấn</h3>
                    
                    {expert.isAvailable ? (
                      <div className="space-y-4">
                        <Link
                          href={`/contact?expert=${expert.id}`}
                          className="w-full group relative inline-flex items-center justify-center px-6 py-3 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                        >
                          <span className="relative z-10">Đặt lịch ngay</span>
                          <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </Link>
                        
                        <div className="text-center">
                          <p className="text-sm text-gray-600 mb-2">Hoặc liên hệ trực tiếp:</p>
                          <p className="text-lg font-semibold text-[#375E5E]">1900-xxxx</p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <div className="w-full inline-flex items-center justify-center px-6 py-3 text-lg font-semibold text-gray-500 bg-gray-100 rounded-2xl cursor-not-allowed mb-4">
                          Hiện không có lịch
                        </div>
                        <p className="text-sm text-gray-600">Vui lòng chọn chuyên gia khác hoặc liên hệ để được hỗ trợ</p>
                      </div>
                    )}
                  </div>

                  {/* Info Card */}
                  <div className="p-6 bg-gray-50 rounded-3xl">
                    <h3 className="text-lg font-bold text-black mb-4">Thông tin chi tiết</h3>
                    
                    <div className="space-y-4">
                      {/* Working Hours */}
                      <div>
                        <h4 className="text-sm font-semibold text-black mb-2">Giờ làm việc:</h4>
                        <p className="text-sm text-gray-700">{expert.workingHours}</p>
                      </div>

                      {/* Languages */}
                      <div>
                        <h4 className="text-sm font-semibold text-black mb-2">Ngôn ngữ:</h4>
                        <div className="flex flex-wrap gap-2">
                          {expert.languages.map((language, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-lg"
                            >
                              {language}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Consultation Types */}
                      <div>
                        <h4 className="text-sm font-semibold text-black mb-2">Hình thức tư vấn:</h4>
                        <div className="flex flex-wrap gap-2">
                          {expert.consultationTypes.map((type, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-lg"
                            >
                              {type}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Education */}
                      <div>
                        <h4 className="text-sm font-semibold text-black mb-2">Học vấn:</h4>
                        <p className="text-sm text-gray-700">{expert.education}</p>
                      </div>

                      {/* Certifications */}
                      <div>
                        <h4 className="text-sm font-semibold text-black mb-2">Chứng chỉ:</h4>
                        <div className="space-y-1">
                          {expert.certifications.map((cert, index) => (
                            <p key={index} className="text-xs text-gray-700">• {cert}</p>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </section>
      </main>
    </div>
  )
}
