import type { Metadata } from "next"
import Script from "next/script"
import Container from "@/components/container"
import ExpertCard from "@/components/experts/expert-card"

export const metadata: Metadata = {
  title: "Đội ngũ chuyên gia tâm lý - Anmy Psychology",
  description: "Gặp gỡ đội ngũ chuyên gia tâm lý giàu kinh nghiệm của Anmy Psychology. Các bác sĩ, thạc sĩ tâm lý với chuyên môn sâu và tận tâm.",
  keywords: ["chuyên gia tâm lý", "bác sĩ tâm lý", "thạc sĩ tâm lý", "đội ngũ chuyên nghiệp", "anmy psychology"],
}

// Mock data - trong thực tế sẽ lấy từ database hoặc CMS
const experts = [
  {
    id: 1,
    name: "ThS. Nguyễn Minh Anh",
    title: "Chuyên gia tâm lý lâm sàng",
    specialties: ["Trầm cảm", "<PERSON> âu", "Stress", "Tâ<PERSON> lý trẻ em"],
    experience: "8+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Quốc gia Hà Nội",
    description: "Chuyên sâu về tâm lý lâm sàng với nhiều năm kinh nghiệm điều trị các rối loạn tâm lý phổ biến.",
    image: "/images/expert-1.jpg",
    languages: ["Tiếng Việt", "English"],
    consultationTypes: ["Trực tiếp", "Online", "Điện thoại"],
    rating: 4.9,
    reviewCount: 127,
    isAvailable: true
  },
  {
    id: 3,
    name: "ThS. Lê Văn Đức",
    title: "Chuyên gia tâm lý gia đình",
    specialties: ["Tâm lý gia đình", "Tâm lý hôn nhân", "Tư vấn cặp đôi"],
    experience: "6+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Sư phạm TP.HCM",
    description: "Chuyên gia tư vấn tâm lý gia đình và hôn nhân, giúp xây dựng mối quan hệ hạnh phúc.",
    image: "/images/expert-3.jpg",
    languages: ["Tiếng Việt", "English"],
    consultationTypes: ["Trực tiếp", "Online"],
    rating: 4.7,
    reviewCount: 64,
    isAvailable: true
  },
  {
    id: 4,
    name: "ThS. Phạm Thị Mai",
    title: "Chuyên gia tâm lý trẻ em",
    specialties: ["Tâm lý trẻ em", "Rối loạn học tập", "ADHD", "Tự kỷ"],
    experience: "10+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Giáo dục",
    description: "Chuyên gia hàng đầu về tâm lý trẻ em với phương pháp điều trị hiện đại và hiệu quả.",
    image: "/images/expert-4.jpg",
    languages: ["Tiếng Việt"],
    consultationTypes: ["Trực tiếp", "Online", "Điện thoại"],
    rating: 4.9,
    reviewCount: 156,
    isAvailable: true
  },
  {
    id: 5,
    name: "ThS. Hoàng Minh Tuấn",
    title: "Chuyên gia tâm lý công sở",
    specialties: ["Stress công việc", "Burnout", "Tâm lý tổ chức", "Leadership"],
    experience: "7+ năm kinh nghiệm",
    education: "Thạc sĩ Tâm lý học - ĐH Kinh tế Quốc dân",
    description: "Chuyên gia tư vấn tâm lý trong môi trường công sở và phát triển kỹ năng lãnh đạo.",
    image: "/images/expert-5.jpg",
    languages: ["Tiếng Việt", "English"],
    consultationTypes: ["Trực tiếp", "Online"],
    rating: 4.6,
    reviewCount: 43,
    isAvailable: true
  },
]

function JsonLdExperts() {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Đội ngũ chuyên gia tâm lý | Anmy Psychology",
    "description": "Gặp gỡ đội ngũ chuyên gia tâm lý giàu kinh nghiệm của Anmy Psychology",
    "url": "https://anmy.com/experts",
    "hasPart": experts.map((expert) => ({
      "@type": "Person",
      "name": expert.name,
      "jobTitle": expert.title,
      "description": expert.description,
      "image": expert.image,
      "worksFor": {
        "@type": "Organization",
        "name": "Anmy Psychology"
      },
      "knowsAbout": expert.specialties,
      "alumniOf": expert.education
    }))
  }

  return (
    <Script
      id="jsonld-experts"
      type="application/ld+json"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  )
}

export default function ExpertsPage() {
  const availableExperts = experts.filter(expert => expert.isAvailable)
  const unavailableExperts = experts.filter(expert => !expert.isAvailable)

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-24 md:py-32 overflow-hidden bg-white">
          {/* Background with animated gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#375E5E]/5 to-transparent animate-pulse"></div>

          {/* Floating elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-[#375E5E] rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-[#375E5E] rounded-full opacity-15 animate-bounce delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-[#375E5E] rounded-full opacity-20 animate-bounce delay-500"></div>

          <Container className="relative z-10">
            <div className="flex flex-col items-center justify-center space-y-8 text-center">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                  Đội ngũ chuyên nghiệp
                </div>
                <h1 className="text-5xl md:text-7xl font-bold tracking-tight text-[#375E5E]">
                  Chuyên gia
                </h1>
                <p className="text-xl md:text-2xl font-medium text-black">
                  Đội ngũ tâm lý giàu kinh nghiệm
                </p>
              </div>

              <p className="max-w-[700px] text-lg md:text-xl text-gray-700 leading-relaxed">
                Gặp gỡ đội ngũ chuyên gia tâm lý giàu kinh nghiệm của chúng tôi. 
                Mỗi chuyên gia đều có chuyên môn sâu và cam kết mang đến sự hỗ trợ tốt nhất cho bạn.
              </p>
            </div>
          </Container>
        </section>

        {/* Available Experts Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-sm font-medium text-green-700 mb-6">
                Đang hoạt động
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 text-black">
                Chuyên gia sẵn sàng hỗ trợ
              </h2>
              <p className="text-xl text-gray-700 max-w-[700px] mx-auto">
                Những chuyên gia hiện đang sẵn sàng tiếp nhận tư vấn và hỗ trợ bạn
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {availableExperts.map((expert) => (
                <ExpertCard key={expert.id} expert={expert} />
              ))}
            </div>
          </Container>
        </section>
      </main>
      <JsonLdExperts />
    </div>
  )
}
