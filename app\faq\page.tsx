import { Metadata } from "next"
import Script from "next/script";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"
import Container from '@/components/container'

export const metadata: Metadata = {
    title: "<PERSON>âu hỏi thường gặp về dịch vụ tâm lý | Anmy Psychology",
    description:
        "Tổng hợp câu hỏi thường gặp về dịch vụ tâm lý: từ tư vấn, li<PERSON>u pháp đến quy trình điều trị và các thông tin quan trọng khác.",
}

function JsonLdFaq() {
    const jsonLd = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": psychologyFaqData.map((item) => ({
            "@type": "Question",
            "name": item.question,
            "acceptedAnswer": {
                "@type": "Answer",
                "text": item.answer,
            },
        })),
    };

    return (
        <Script
            id="jsonld-faq"
            type="application/ld+json"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
    );
}

export default function FAQPage() {
    return (
        <div className="flex min-h-[calc(100vh-4rem)] flex-col bg-white">
            <main className="flex-1">
                <section className="w-full py-12 bg-white">
                    <Container>
                        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                            <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-black">Câu hỏi thường gặp</h1>
                            <p className="max-w-[700px] text-lg text-gray-700">
                                Tìm hiểu thêm về dịch vụ tâm lý của chúng tôi qua những câu hỏi thường gặp
                            </p>
                        </div>
                        <Accordion
                            type="single"
                            collapsible
                            className="w-full mx-auto"
                        >
                            {psychologyFaqData.map((item, index) => (
                                <AccordionItem value={`item-${index + 1}`} key={index}>
                                    <AccordionTrigger>{item.question}</AccordionTrigger>
                                    <AccordionContent>{item.answer}</AccordionContent>
                                </AccordionItem>
                            ))}
                        </Accordion>
                    </Container>
                </section>
            </main>
            <JsonLdFaq />
        </div>
    )
}


const psychologyFaqData = [
    {
        question: "Dịch vụ tâm lý Anmy cung cấp những gì?",
        answer:
            "Anmy cung cấp các dịch vụ tâm lý chuyên nghiệp bao gồm: tư vấn tâm lý cá nhân, liệu pháp tâm lý, đánh giá tâm lý, liệu pháp nhóm và hỗ trợ sức khỏe tinh thần.",
    },
    {
        question: "Khi nào tôi nên tìm kiếm sự hỗ trợ tâm lý?",
        answer:
            "Bạn nên tìm kiếm hỗ trợ khi cảm thấy căng thẳng, lo âu, trầm cảm, gặp khó khăn trong các mối quan hệ, hoặc khi muốn phát triển bản thân và cải thiện chất lượng cuộc sống.",
    },
    {
        question: "Quy trình tư vấn tâm lý diễn ra như thế nào?",
        answer:
            "Quy trình bao gồm: đánh giá ban đầu, xác định mục tiêu điều trị, lập kế hoạch can thiệp, thực hiện các buổi tư vấn/liệu pháp, và theo dõi tiến triển.",
    },
    {
        question: "Một buổi tư vấn kéo dài bao lâu?",
        answer:
            "Thông thường một buổi tư vấn kéo dài từ 45-60 phút. Buổi đánh giá ban đầu có thể kéo dài lâu hơn (60-90 phút).",
    },
    {
        question: "Thông tin cá nhân của tôi có được bảo mật không?",
        answer:
            "Hoàn toàn bảo mật. Chúng tôi tuân thủ nghiêm ngặt các quy định về bảo mật thông tin khách hàng và đạo đức nghề nghiệp tâm lý.",
    },
    {
        question: "Tôi có thể tư vấn online không?",
        answer:
            "Có, chúng tôi cung cấp dịch vụ tư vấn tâm lý online qua video call an toàn và bảo mật, phù hợp cho những ai không thể đến trực tiếp.",
    },
    {
        question: "Chi phí dịch vụ tâm lý là bao nhiêu?",
        answer:
            "Chi phí tùy thuộc vào loại dịch vụ và thời gian. Vui lòng liên hệ với chúng tôi để được tư vấn chi tiết về gói dịch vụ phù hợp.",
    },
    {
        question: "Bao lâu thì tôi thấy được kết quả?",
        answer:
            "Thời gian thấy kết quả tùy thuộc vào tình trạng cá nhân và mức độ tham gia. Một số người cảm thấy tốt hơn sau vài buổi, số khác cần thời gian dài hơn.",
    },
    {
        question: "Làm thế nào để đặt lịch hẹn?",
        answer:
            "Bạn có thể đặt lịch qua website, gọi điện thoại, hoặc gửi tin nhắn. Chúng tôi sẽ sắp xếp thời gian phù hợp với lịch trình của bạn.",
    },
    {
        question: "Anmy có hỗ trợ khẩn cấp không?",
        answer:
            "Chúng tôi có đường dây hỗ trợ khẩn cấp cho các trường hợp cần can thiệp ngay lập tức. Trong tình huống khẩn cấp, vui lòng liên hệ ngay với chúng tôi hoặc các dịch vụ cấp cứu.",
    },
]