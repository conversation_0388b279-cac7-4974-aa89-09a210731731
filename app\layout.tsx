import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import type { Metadata, Viewport } from "next"
import { ThemeProvider } from "@/components/theme-provider"
import NavBar from "@/components/navbar"
import Footer from "@/components/footer"
import Script from "next/script"
import GoogleAnalytics from "@/components/googleanalytics"
import FloatingButtons from "@/components/floatingbuttons"

const inter = Inter({ subsets: ["latin"] })

export const viewport: Viewport = {
  themeColor: "#ffffff",
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
}

export const metadata: Metadata = {
  metadataBase: new URL("https://tamlyanmy.com"),
  title: {
    default: "Dịch vụ tâm lý Anmy - Chăm sóc sức khỏe tinh thần",
    template: "%s | Anmy Psychology",
  },
  description:
    "Dịch vụ tâm lý chuyên nghiệp <PERSON> - <PERSON><PERSON> vấn tâm lý, li<PERSON><PERSON> phá<PERSON> tâm lý, đ<PERSON>h giá tâm lý. <PERSON><PERSON><PERSON> hành cùng bạn trên hành trình chăm sóc sức khỏe tinh thần.",
  applicationName: "Anmy Psychology",
  authors: [{ name: "Anmy Psychology Team" }],
  generator: "Next.js",
  keywords: ["tâm lý", "tư vấn tâm lý", "liệu pháp tâm lý", "sức khỏe tinh thần", "anmy", "chăm sóc tâm lý"],
  referrer: "origin-when-cross-origin",
  creator: "Anmy Psychology",
  publisher: "Anmy Psychology",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-video-preview": -1,
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
    ],
  },
  manifest: "/manifest.json",
  openGraph: {
    type: "website",
    locale: "vi_VN",
    url: "https://tamlyanmy.com",
    siteName: "Anmy Psychology",
    title: "Dịch vụ tâm lý Anmy - Chăm sóc sức khỏe tinh thần",
    description:
      "Dịch vụ tâm lý chuyên nghiệp Anmy - Tư vấn tâm lý, liệu pháp tâm lý, đánh giá tâm lý. Đồng hành cùng bạn trên hành trình chăm sóc sức khỏe tinh thần.",
    images: [
      {
        url: "https://tamlyanmy.com/wp-content/uploads/2025/06/cropped-Frame-427319175-768x191.png",
        width: 800,
        height: 600,
        alt: "Anmy Psychology - Dịch vụ tâm lý chuyên nghiệp",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Dịch vụ tâm lý Anmy - Chăm sóc sức khỏe tinh thần",
    description:
      "Dịch vụ tâm lý chuyên nghiệp Anmy - Tư vấn tâm lý, liệu pháp tâm lý, đánh giá tâm lý. Đồng hành cùng bạn trên hành trình chăm sóc sức khỏe tinh thần.",
    images: ["https://tamlyanmy.com/wp-content/uploads/2025/06/cropped-Frame-427319175-768x191.png"],
    creator: "@anmypsychology",
    site: "@anmypsychology",
  },
  alternates: {
    canonical: "https://tamlyanmy.com",
    languages: {
      "vi-VN": "https://tamlyanmy.com",
    },
  },
  category: "health",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#ffffff" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
        <Script id="organization-schema" type="application/ld+json" strategy="afterInteractive">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Anmy Psychology",
            "url": "https://tamlyanmy.com",
            "logo": {
              "@type": "ImageObject",
              "url": "https://tamlyanmy.com/wp-content/uploads/2025/06/cropped-Frame-427319175-768x191.png",
              "width": 768,
              "height": 191
            },
            "sameAs": [
              "https://www.facebook.com/anmypsychology",
              "https://www.youtube.com/@anmypsychology",
              "https://www.tiktok.com/@anmypsychology",
              "https://www.instagram.com/anmypsychology"
            ]
          })}
        </Script>

      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <NavBar />
          {children}
          <FloatingButtons />
          <GoogleAnalytics />
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  )
}
