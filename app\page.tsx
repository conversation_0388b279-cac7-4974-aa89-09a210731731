import type { Metadata } from "next"
import Container from "@/components/container"
import PostGrid from "@/components/blog/post-grid"
import { getBlogPosts } from "@/utils/wordpress"

export const metadata: Metadata = {
  title: "Trang chủ - Dịch vụ tâm lý Anmy",
  description: "<PERSON>ịch vụ tâm lý chuyên nghiệ<PERSON> - Tư vấn tâm lý, li<PERSON><PERSON> ph<PERSON><PERSON> tâm lý, đ<PERSON>h gi<PERSON> tâm lý. Đồng hành cùng bạn trên hành trình chăm sóc sức khỏe tinh thần.",
}

export default async function HomePage() {
  // Lấy bài viết mới nhất để hiển thị
  const posts = await getBlogPosts(6);

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-24 md:py-32 overflow-hidden bg-white">
          {/* Background with animated gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#375E5E]/5 to-transparent animate-pulse"></div>

          {/* Floating elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-[#375E5E] rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-[#375E5E] rounded-full opacity-15 animate-bounce delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-[#375E5E] rounded-full opacity-20 animate-bounce delay-500"></div>

          <Container className="relative z-10">
            <div className="flex flex-col items-center justify-center space-y-8 text-center">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                  Dịch vụ tâm lý chuyên nghiệp
                </div>
                <h1 className="text-5xl md:text-7xl font-bold tracking-tight text-[#375E5E]">
                  Anmy Psychology
                </h1>
                <p className="text-xl md:text-2xl font-medium text-black">
                  Trung tâm tâm lý uy tín và chuyên nghiệp
                </p>
              </div>

              <p className="max-w-[700px] text-lg md:text-xl text-gray-700 leading-relaxed">
                Chúng tôi cung cấp các dịch vụ tâm lý chuyên nghiệp với đội ngũ chuyên gia giàu kinh nghiệm,
                cam kết mang đến sự hỗ trợ tốt nhất cho sức khỏe tinh thần của bạn.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <a href="/contact" className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <span className="relative z-10">Đặt lịch tư vấn</span>
                  <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>

                <a href="/quiz" className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E]/80 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <span className="relative z-10">Trắc nghiệm tâm lý</span>
                  <div className="absolute inset-0 bg-[#375E5E] rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>

                <a href="/services" className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-white backdrop-blur-sm rounded-2xl border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#375E5E]">
                  Xem dịch vụ
                </a>
              </div>
            </div>
          </Container>
        </section>
        <section className="w-full py-20 bg-gray-50">
          <Container>
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                Tin tức & Kiến thức
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 text-black">
                Bài viết mới nhất
              </h2>
              <p className="text-xl text-gray-700 max-w-[700px] mx-auto">
                Cập nhật những kiến thức và thông tin mới nhất về sức khỏe tinh thần và tâm lý học
              </p>
            </div>

            {posts.length > 0 ? (
              <div className="space-y-8">
                {/* Featured Posts Grid */}
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {posts.map((post, index) => (
                    <article key={post.id} className="group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                      {/* Post Image */}
                      <div className="relative h-48 bg-gray-100 overflow-hidden">
                        {post.coverImage ? (
                          <img
                            src={post.coverImage}
                            alt={post.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-[#375E5E]/20 to-[#375E5E]/40 flex items-center justify-center">
                            <svg className="w-12 h-12 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                        )}

                        {/* Featured Badge for first post */}
                        {index === 0 && (
                          <div className="absolute top-4 left-4">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#375E5E] text-white">
                              Nổi bật
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Post Content */}
                      <div className="p-6">
                        <div className="mb-3">
                          <time className="text-sm text-gray-500">
                            {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('vi-VN') : 'Mới cập nhật'}
                          </time>
                        </div>

                        <h3 className="text-xl font-bold text-black mb-3 line-clamp-2 group-hover:text-[#375E5E] transition-colors duration-300">
                          <a href={`/blog/${post.slug}`}>
                            {post.title}
                          </a>
                        </h3>

                        {post.excerpt && (
                          <p className="text-gray-700 text-sm leading-relaxed line-clamp-3 mb-4">
                            {post.excerpt.replace(/<[^>]*>/g, '')}
                          </p>
                        )}

                        <a
                          href={`/blog/${post.slug}`}
                          className="inline-flex items-center text-[#375E5E] font-medium text-sm hover:text-[#375E5E]/80 transition-colors duration-300"
                        >
                          Đọc thêm
                          <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </a>
                      </div>
                    </article>
                  ))}
                </div>

                {/* View All Button */}
                <div className="text-center mt-12">
                  <a
                    href="/blog"
                    className="group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <span className="relative z-10">Xem tất cả bài viết</span>
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                    <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </a>
                </div>
              </div>
            ) : (
              <div className="text-center py-20">
                <div className="max-w-md mx-auto">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-2xl bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-12 h-12 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-black mb-2">Đang cập nhật nội dung</h3>
                  <p className="text-gray-700">Chúng tôi đang chuẩn bị những bài viết chất lượng. Vui lòng quay lại sau!</p>
                </div>
              </div>
            )}
          </Container>
        </section>
        {/* Services Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                Dịch vụ chuyên nghiệp
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 text-black">
                Dịch vụ tâm lý toàn diện
              </h2>
              <p className="text-xl text-gray-700 max-w-[700px] mx-auto">
                Chúng tôi cung cấp các dịch vụ tâm lý chuyên nghiệp với phương pháp hiện đại và hiệu quả
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-3">
              {/* Tư vấn cá nhân */}
              <div className="group relative p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-[#375E5E]">
                <div className="absolute inset-0 bg-[#375E5E]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-black">Tư vấn cá nhân</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Dịch vụ tư vấn tâm lý cá nhân với chuyên gia giàu kinh nghiệm.
                    Không gian riêng tư, bảo mật tuyệt đối để bạn thoải mái chia sẻ.
                  </p>
                  <div className="flex items-center text-[#375E5E] font-medium group-hover:text-[#375E5E]/80">
                    Tìm hiểu thêm →
                  </div>
                </div>
              </div>

              {/* Liệu pháp nhóm */}
              <div className="group relative p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-[#375E5E]">
                <div className="absolute inset-0 bg-[#375E5E]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-black">Liệu pháp nhóm</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Tham gia các nhóm hỗ trợ với những người có cùng vấn đề.
                    Chia sẻ kinh nghiệm và cùng nhau phục hồi trong môi trường tích cực.
                  </p>
                  <div className="flex items-center text-[#375E5E] font-medium group-hover:text-[#375E5E]/80">
                    Tìm hiểu thêm →
                  </div>
                </div>
              </div>

              {/* Đánh giá tâm lý */}
              <div className="group relative p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-[#375E5E]">
                <div className="absolute inset-0 bg-[#375E5E]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-black">Đánh giá tâm lý</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Đánh giá toàn diện tình trạng sức khỏe tinh thần với các công cụ
                    chuyên nghiệp và phương pháp khoa học hiện đại.
                  </p>
                  <div className="flex items-center text-[#375E5E] font-medium group-hover:text-[#375E5E]/80">
                    Tìm hiểu thêm →
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </section>

        {/* Why Choose Us Section */}
        <section className="w-full py-20 relative overflow-hidden bg-[#375E5E]">
          <div className="absolute inset-0 opacity-10">
            <div className="w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>

          <Container className="relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6 text-white">
                Tại sao chọn <span className="text-white">Anmy Psychology</span>?
              </h2>
              <p className="text-xl text-white/80 max-w-[700px] mx-auto">
                Chúng tôi cam kết mang đến dịch vụ tâm lý chất lượng cao với phương pháp chuyên nghiệp
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center group">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Chuyên nghiệp</h3>
                <p className="text-white/80 text-sm leading-relaxed">
                  Đội ngũ chuyên gia tâm lý có bằng cấp và kinh nghiệm thực tế.
                </p>
              </div>

              <div className="text-center group">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Tận tâm</h3>
                <p className="text-white/80 text-sm leading-relaxed">
                  Lắng nghe không phán xét, đồng hành cùng bạn trong mọi khó khăn.
                </p>
              </div>

              <div className="text-center group">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Linh hoạt</h3>
                <p className="text-white/80 text-sm leading-relaxed">
                  Tư vấn trực tiếp, online hoặc qua điện thoại theo nhu cầu của bạn.
                </p>
              </div>

              <div className="text-center group">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Bảo mật</h3>
                <p className="text-white/80 text-sm leading-relaxed">
                  Thông tin khách hàng được bảo mật tuyệt đối theo chuẩn quốc tế.
                </p>
              </div>
            </div>

            <div className="text-center mt-16">
              <a href="/quiz" className="inline-flex items-center px-8 py-4 text-lg font-semibold text-[#375E5E] bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Bắt đầu với trắc nghiệm miễn phí
              </a>
            </div>
          </Container>
        </section>

        {/* News/Blog Section */}


        {/* CTA Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="text-center space-y-8">
              <div className="space-y-4">
                <h2 className="text-4xl md:text-5xl font-bold tracking-tight text-[#375E5E]">
                  Bắt đầu hành trình chăm sóc tâm lý
                </h2>
                <p className="text-xl text-gray-700 max-w-[600px] mx-auto">
                  Đừng để các vấn đề tâm lý ảnh hưởng đến chất lượng cuộc sống.
                  Hãy liên hệ với chúng tôi để được hỗ trợ chuyên nghiệp.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <span className="relative z-10">Đặt lịch tư vấn</span>
                  <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>

                <a href="/faq" className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-white backdrop-blur-sm rounded-2xl border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#375E5E]">
                  Câu hỏi thường gặp
                </a>
              </div>

              <p className="text-sm text-gray-600">
                Chúng tôi luôn sẵn sàng hỗ trợ bạn với dịch vụ chuyên nghiệp và tận tâm.
              </p>
            </div>
          </Container>
        </section>
      </main>
    </div>
  );
}