import type { Metadata } from "next"
import Container from "@/components/container"
import QuizComponent from "@/components/quiz/quiz-component"

export const metadata: Metadata = {
  title: "Trắc nghiệm Tâm lý - Anmy Psychology",
  description: "<PERSON><PERSON>h giá sức khỏe tinh thần miễn phí với trắc nghiệm chuyên nghiệp. 20 câu hỏi được chọn ngẫu nhiên từ 50 câu hỏi do chuyên gia thiết kế.",
  keywords: ["trắc nghiệm tâm lý", "đánh giá sức khỏe tinh thần", "test tâm lý chuyên nghiệp", "anmy psychology", "tự đánh giá tâm lý"],
}

export default function QuizPage() {
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-20 overflow-hidden bg-white">
          {/* Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>

          {/* Floating elements */}
          <div className="absolute top-10 left-10 w-16 h-16 bg-[#375E5E] rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-32 right-20 w-12 h-12 bg-[#375E5E] rounded-full opacity-15 animate-bounce delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-10 h-10 bg-[#375E5E] rounded-full opacity-20 animate-bounce delay-500"></div>

          <Container className="relative z-10">
            <div className="flex flex-col items-center justify-center space-y-8 text-center">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
                  Đánh giá tâm lý chuyên nghiệp
                </div>

                <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
                  <span className="text-[#375E5E]">
                    Trắc nghiệm Tâm lý
                  </span>
                  <br />
                  <span className="text-2xl md:text-3xl font-medium text-black">
                    Đánh giá sức khỏe tinh thần
                  </span>
                </h1>
              </div>

              <p className="max-w-[700px] text-lg md:text-xl text-gray-700 leading-relaxed">
                Đánh giá tình trạng sức khỏe tinh thần của bạn thông qua bộ câu hỏi chuyên nghiệp.
                Kết quả sẽ giúp bạn hiểu rõ hơn về bản thân và đưa ra những quyết định phù hợp.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>5 phút</span>
                  <span>•</span>
                  <span>Miễn phí</span>
                  <span>•</span>
                  <span>Bảo mật</span>
                </div>
              </div>
            </div>
          </Container>
        </section>

        {/* Quiz Section */}
        <section className="w-full py-12 bg-white">
          <Container>
            <QuizComponent />
          </Container>
        </section>

        {/* Info Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-black">
                  Về trắc nghiệm này
                </h2>
                <p className="text-xl text-gray-700">
                  Hiểu rõ về bản thân là bước đầu quan trọng để cải thiện chất lượng cuộc sống
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3 mb-16">
                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">Cách hoạt động</h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>50 câu hỏi được thiết kế bởi chuyên gia</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Random 20 câu mỗi lần test</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Đánh giá 5 lĩnh vực tâm lý</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Kết quả ngay lập tức</span>
                    </li>
                  </ul>
                </div>

                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">Bạn sẽ biết</h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Mức độ căng thẳng hiện tại</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Điểm mạnh của bản thân</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Những điều cần cải thiện</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Gợi ý phát triển bản thân</span>
                    </li>
                  </ul>
                </div>

                <div className="group p-8 rounded-3xl bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 hover:border-[#375E5E]">
                  <div className="w-16 h-16 rounded-2xl bg-[#375E5E] flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-black">An toàn & Riêng tư</h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>100% bảo mật thông tin</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Không lưu trữ dữ liệu cá nhân</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Không phán xét, chỉ hỗ trợ</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-[#375E5E] mt-1">•</span>
                      <span>Hoàn toàn miễn phí</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Important Note */}
              <div className="p-8 bg-[#375E5E]/5 rounded-3xl border border-[#375E5E]/20 mb-12">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 rounded-full bg-[#375E5E] flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-[#375E5E] mb-2 text-lg">
                      Lưu ý quan trọng
                    </h3>
                    <p className="text-gray-700 leading-relaxed">
                      Trắc nghiệm này chỉ mang tính chất tham khảo, không thay thế được việc tư vấn trực tiếp với chuyên gia tâm lý.
                      Nếu bạn cảm thấy cần hỗ trợ chuyên nghiệp, vui lòng liên hệ với chúng tôi.
                    </p>
                  </div>
                </div>
              </div>

              {/* CTA */}
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-4 text-black">Cần hỗ trợ chuyên nghiệp?</h3>
                <p className="text-gray-700 mb-8 text-lg">
                  Đội ngũ tâm lý của Anmy luôn sẵn sàng đồng hành cùng bạn
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/contact"
                    className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <span className="relative z-10">Liên hệ tư vấn</span>
                    <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </a>
                  <a
                    href="/services"
                    className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-white backdrop-blur-sm rounded-2xl border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#375E5E]"
                  >
                    Khám phá dịch vụ
                  </a>
                </div>
              </div>
            </div>
          </Container>
        </section>
      </main>
    </div>
  )
}
