import type { Metadata } from "next"
import Container from "@/components/container"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Dịch vụ Tâm lý Chuyên nghiệp - Anmy Psychology",
  description: "Khám phá các dịch vụ tâm lý chuyên nghiệp của Anmy: tư vấn cá nhân, li<PERSON><PERSON> pháp tâm lý, đ<PERSON>h gi<PERSON> tâm lý và liệu pháp nhóm với đội ngũ chuyên gia giàu kinh nghiệm.",
  keywords: ["dịch vụ tâm lý chuyên nghiệp", "tư vấn tâm lý", "li<PERSON>u pháp tâm lý", "đánh giá tâm lý", "liệu pháp nhóm", "anmy psychology"],
}

export default function ServicesPage() {
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="flex flex-col items-center justify-center space-y-6 text-center">
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight text-black">
                Dịch vụ <span className="text-[#375E5E]">Tâm lý</span> Chuyên nghiệp
              </h1>
              <p className="max-w-[800px] text-lg md:text-xl text-gray-700">
                Chúng tôi cung cấp các dịch vụ tâm lý toàn diện để hỗ trợ sức khỏe tinh thần và phát triển bản thân của bạn
              </p>
            </div>
          </Container>
        </section>

        {/* Services Grid */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2">
              {/* Tư vấn tâm lý */}
              <div className="group relative overflow-hidden rounded-lg border border-gray-200 bg-white p-8 hover:shadow-lg transition-all hover:border-[#375E5E]">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 rounded-full bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-8 h-8 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-black">Tư vấn tâm lý</h3>
                </div>
                <p className="text-gray-700 mb-6">
                  Hỗ trợ giải quyết các vấn đề tâm lý, căng thẳng, lo âu, trầm cảm và các khó khăn trong cuộc sống.
                  Chúng tôi lắng nghe và đồng hành cùng bạn tìm ra giải pháp phù hợp.
                </p>
                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                  <li>• Tư vấn cá nhân 1-1</li>
                  <li>• Tư vấn online an toàn</li>
                  <li>• Hỗ trợ khủng hoảng tâm lý</li>
                  <li>• Tư vấn các vấn đề gia đình</li>
                </ul>
                <Link
                  href="/services/counseling"
                  className="inline-flex items-center text-[#375E5E] hover:text-[#375E5E]/80 font-medium"
                >
                  Tìm hiểu thêm →
                </Link>
              </div>

              {/* Liệu pháp tâm lý */}
              <div className="group relative overflow-hidden rounded-lg border border-gray-200 bg-white p-8 hover:shadow-lg transition-all hover:border-[#375E5E]">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 rounded-full bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-8 h-8 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-black">Liệu pháp tâm lý</h3>
                </div>
                <p className="text-gray-700 mb-6">
                  Các phương pháp điều trị chuyên sâu cho các rối loạn tâm lý như trầm cảm, lo âu, rối loạn căng thẳng sau chấn thương,
                  và các vấn đề tâm lý phức tạp khác.
                </p>
                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                  <li>• Liệu pháp nhận thức hành vi (CBT)</li>
                  <li>• Liệu pháp tâm lý động lực</li>
                  <li>• Liệu pháp gia đình</li>
                  <li>• Liệu pháp nghệ thuật</li>
                </ul>
                <Link
                  href="/services/therapy"
                  className="inline-flex items-center text-[#375E5E] hover:text-[#375E5E]/80 font-medium"
                >
                  Tìm hiểu thêm →
                </Link>
              </div>

              {/* Đánh giá tâm lý */}
              <div className="group relative overflow-hidden rounded-lg border border-gray-200 bg-white p-8 hover:shadow-lg transition-all hover:border-[#375E5E]">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 rounded-full bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-8 h-8 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-black">Đánh giá tâm lý</h3>
                </div>
                <p className="text-gray-700 mb-6">
                  Đánh giá toàn diện tình trạng sức khỏe tinh thần, khả năng nhận thức, tính cách và đưa ra lời khuyên
                  phù hợp cho từng cá nhân.
                </p>
                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                  <li>• Đánh giá trí tuệ và nhận thức</li>
                  <li>• Đánh giá tính cách</li>
                  <li>• Đánh giá sức khỏe tinh thần</li>
                  <li>• Báo cáo chi tiết và khuyến nghị</li>
                </ul>
                <Link
                  href="/services/assessment"
                  className="inline-flex items-center text-[#375E5E] hover:text-[#375E5E]/80 font-medium"
                >
                  Tìm hiểu thêm →
                </Link>
              </div>

              {/* Liệu pháp nhóm */}
              <div className="group relative overflow-hidden rounded-lg border border-gray-200 bg-white p-8 hover:shadow-lg transition-all hover:border-[#375E5E]">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 rounded-full bg-[#375E5E]/10 flex items-center justify-center">
                    <svg className="w-8 h-8 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-black">Liệu pháp nhóm</h3>
                </div>
                <p className="text-gray-700 mb-6">
                  Tham gia các nhóm hỗ trợ và liệu pháp nhóm để chia sẻ kinh nghiệm, học hỏi từ người khác
                  và cùng nhau vượt qua khó khăn.
                </p>
                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                  <li>• Nhóm hỗ trợ lo âu - trầm cảm</li>
                  <li>• Nhóm kỹ năng sống</li>
                  <li>• Nhóm phát triển bản thân</li>
                  <li>• Nhóm hỗ trợ gia đình</li>
                </ul>
                <Link
                  href="/services/group-therapy"
                  className="inline-flex items-center text-[#375E5E] hover:text-[#375E5E]/80 font-medium"
                >
                  Tìm hiểu thêm →
                </Link>
              </div>
            </div>
          </Container>
        </section>

        {/* CTA Section */}
        <section className="w-full py-20 bg-white">
          <Container>
            <div className="text-center space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-black">Sẵn sàng bắt đầu hành trình chăm sóc tâm lý?</h2>
              <p className="text-lg text-gray-700 max-w-[600px] mx-auto">
                Đừng để các vấn đề tâm lý ảnh hưởng đến chất lượng cuộc sống. Hãy liên hệ với chúng tôi ngay hôm nay.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-md bg-[#375E5E] px-8 py-3 text-sm font-medium text-white shadow transition-colors hover:bg-[#375E5E]/90"
                >
                  Đặt lịch tư vấn
                </Link>
                <Link
                  href="/faq"
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-8 py-3 text-sm font-medium text-black shadow-sm transition-colors hover:border-[#375E5E]"
                >
                  Câu hỏi thường gặp
                </Link>
              </div>
            </div>
          </Container>
        </section>
      </main>
    </div>
  )
}
