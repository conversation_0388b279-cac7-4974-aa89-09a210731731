import Script from "next/script";
import { Metadata } from "next";
import Link from "next/link";
import BreadcrumbUI from "@/components/breadcrumb";

const tagCategories = {
  "Sức khỏe tinh thần": [
    "sức khỏe tinh thần",
    "tâm lý học",
    "căng thẳng",
    "lo âu",
    "trầm cảm",
    "tự tin",
    "phát triển bản thân",
    "quản lý cảm xúc",
    "k<PERSON> năng sống",
    "mindfulness",
  ],
  "Liệu pháp tâm lý": [
    "liệu pháp nhận thức hành vi",
    "CBT",
    "liệu pháp tâm lý",
    "tư vấn tâm lý",
    "liệu pháp nhóm",
    "liệu pháp gia đình",
    "liệu pháp nghệ thuật",
    "điều trị tâm lý",
    "phục hồi tâm lý",
  ],
  "Rối loạn tâm lý": [
    "rối loạn lo âu",
    "rối loạn trầm cảm",
    "rối loạn lưỡng cực",
    "rối loạn ăn uống",
    "rối loạn giấc ngủ",
    "PTSD",
    "rối loạn stress",
  ],
  "Tâm lý trẻ em": [
    "tâm lý trẻ em",
    "tâm lý vị thành niên",
    "rối loạn học tập",
    "tự kỷ",
    "ADHD",
    "tâm lý phát triển",
  ],
  "Tâm lý gia đình": [
    "tâm lý gia đình",
    "tư vấn hôn nhân",
    "xung đột gia đình",
    "nuôi dạy con",
    "mối quan hệ",
    "giao tiếp gia đình",
  ],
  "Kỹ năng tâm lý": [
    "kỹ năng giao tiếp",
    "quản lý thời gian",
    "tư duy tích cực",
    "thiền định",
    "yoga tâm lý",
    "thư giãn",
  ],
  "Đánh giá tâm lý": [
    "trắc nghiệm tâm lý",
    "đánh giá IQ",
    "đánh giá EQ",
    "test tính cách",
    "đánh giá năng lực",
    "báo cáo tâm lý",
  ],
};

const getAllTags = () => {
  const allTags: string[] = [];
  Object.values(tagCategories).forEach(tags => {
    allTags.push(...tags);
  });
  return allTags;
};

const allTags = getAllTags();

export const metadata: Metadata = {
  title: "Danh sách Tags Tâm lý | Khám phá các Chủ đề Sức khỏe Tinh thần | Anmy Psychology",
  description:
    "Tổng hợp các thẻ tag về tâm lý, sức khỏe tinh thần, liệu pháp, tư vấn tâm lý và nhiều chủ đề hữu ích khác từ Anmy Psychology.",
  keywords: allTags.slice(0, 20).join(", "),
  alternates: {
    canonical: "https://tamlyanmy.com/tags",
  },
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "Danh sách Tags Tâm lý | Khám phá các Chủ đề Sức khỏe Tinh thần | Anmy Psychology",
    description:
      "Khám phá danh sách tags phổ biến nhất liên quan đến tâm lý, sức khỏe tinh thần, liệu pháp và tư vấn tâm lý từ Anmy Psychology.",
    url: "https://anmy.com/tags",
    siteName: "Anmy Psychology",
    type: "website",
    locale: "vi_VN",
    images: [
      {
        url: "https://anmy.com/images/tags-banner.jpg",
        width: 800,
        height: 600,
        alt: "Tags Tâm lý - Anmy Psychology",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Danh sách Tags Tâm lý | Anmy Psychology",
    description:
      "Tổng hợp các tags về tâm lý, sức khỏe tinh thần, liệu pháp và tư vấn tâm lý từ Anmy Psychology.",
    images: ["https://anmy.com/images/tags-banner.jpg"],
  },
};

const generateSchemaMarkup = () => {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Trang chủ",
        item: "https://global-mmo.com"
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Danh sách Tags MMO",
        item: "https://global-mmo.com/tags"
      }
    ]
  };

  const itemList = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: "Danh sách thẻ Tags từ Global MMO",
    description: "Tổng hợp các tags phổ biến về MMO, kiếm tiền online và digital marketing.",
    numberOfItems: allTags.length,
    itemListElement: allTags.map((tag, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@type": "Thing",
        name: tag,
        url: `https://global-mmo.com/tags/${tag.replace(/\s+/g, '-').toLowerCase()}`,
        description: `Bài viết và tài nguyên về ${tag} từ Global MMO`,
      },
    })),
  };

  const webSite = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Global MMO",
    url: "https://global-mmo.com",
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: "https://global-mmo.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  return [breadcrumbSchema, itemList, webSite];
};

const tagToSlug = (tag: string) => {
  return tag.replace(/\s+/g, '-').toLowerCase();
};

export default function TagsPage() {
  return (
    <main className="container mx-auto px-4 py-10">
      <BreadcrumbUI nameFourth="Danh sách Tags MMO" />
      <h1 className="text-3xl md:text-4xl font-bold my-4">Danh sách Tags MMO</h1>

      <p className="mb-8">
        Khám phá hơn 40+ chủ đề phổ biến về MMO, affiliate marketing, kiếm tiền online, dropshipping và digital marketing.
        Tại Global MMO, chúng tôi tổng hợp các xu hướng và phương pháp kiếm tiền trực tuyến mới nhất giúp bạn bắt đầu
        hành trình kiếm tiền online của mình một cách hiệu quả.
      </p>

      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Tags phổ biến</h2>
        <div className="flex flex-wrap gap-3">
          {["kiếm tiền online", "MMO", "affiliate marketing", "kiếm tiền TikTok", "SEO website", "dropshipping"].map((tag) => (
            <div
              className="px-5 py-2 font-medium text-sm rounded-full hover:opacity-90 transition"
            >
              #{tag}
            </div>
          ))}
        </div>
      </div>

      {Object.entries(tagCategories).map(([category, tags]) => (
        <section key={category} className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{category}</h2>
          <div className="flex flex-wrap gap-3">
            {tags.map((tag) => (
              <div
                className="text-sm px-4 py-1.5 rounded-full border border-gray-300 shadow-sm transition"
              >
                #{tag}
              </div>
            ))}
          </div>
        </section>
      ))}
      <Script
        id="jsonld-tags"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(generateSchemaMarkup()) }}
      />
    </main>
  );
}