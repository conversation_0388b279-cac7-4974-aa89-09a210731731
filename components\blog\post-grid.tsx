import Link from "next/link"
import Container from "@/components/container"
import { Button } from "@/components/ui/button"
import PostList from "./post-list"

interface PostGridProps {
  posts: any[]
  showViewAll?: boolean
}

export default function PostGrid({ posts, showViewAll = true }: PostGridProps) {
  if (!posts || posts.length === 0) return null
  return (
    <Container>
      <div className="grid gap-10 md:grid-cols-2 lg:gap-10">
        {posts.slice(0, 2).map((post) => (
          <PostList key={post.id} post={post} aspect="landscape" preloadImage={true} />
        ))}
      </div>
      <div className="mt-10 grid gap-10 md:grid-cols-2 lg:gap-10 xl:grid-cols-4">
        {posts.slice(2, 14).map((post) => (
          <PostList key={post.id} post={post} aspect="square" />
        ))}
      </div>
      {showViewAll && posts.length > 14 && (
        <div className="mt-10 flex justify-center">
          <Button variant="outline" asChild>
            <Link href="/blog">Xem tất cả bài viết</Link>
          </Button>
        </div>
      )}
    </Container>
  )
}