import Image from "next/image"
import Link from "next/link"
import { parseISO, format } from "date-fns"
import { ImageIcon as PhotoIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { vi } from "date-fns/locale"

interface PostListProps {
  post: any
  aspect?: "square" | "landscape" | "custom"
  minimal?: boolean
  pathPrefix?: string
  preloadImage?: boolean
  fontSize?: "normal" | "large"
  fontWeight?: "normal" | "bold"
}

export default function PostList({
  post,
  aspect = "landscape",
  minimal = false,
  pathPrefix = "",
  preloadImage = false,
  fontSize = "normal",
  fontWeight = "bold",
}: PostListProps) {
  return (
    <>
      <div className={cn("group cursor-pointer", minimal && "grid gap-10 md:grid-cols-2")}>
        <div className={cn("overflow-hidden rounded-md bg-gray-100 transition-all hover:scale-105 dark:bg-gray-800")}>
          <Link
            className={cn(
              "relative block",
              aspect === "landscape" ? "aspect-video" : aspect === "custom" ? "aspect-[5/4]" : "aspect-square",
            )}
            href={`/blog/${post.slug}`}
          >
            {post.coverImage ? (
              <Image
                src={post.coverImage || "/logo.webp"}
                alt={post.title || "Thumbnail"}
                priority={preloadImage ? true : false}
                className="object-cover transition-all"
                fill
                sizes="(max-width: 768px) 30vw, 33vw"
              />
            ) : (
              <span className="absolute left-1/2 top-1/2 h-16 w-16 -translate-x-1/2 -translate-y-1/2 text-gray-200">
                <PhotoIcon />
              </span>
            )}
          </Link>
        </div>

        <div className={cn(minimal && "flex items-center")}>
          <div>
            <h2
              className={cn(
                fontSize === "large" ? "text-2xl" : minimal ? "text-3xl" : "text-lg",
                fontWeight === "normal"
                  ? "line-clamp-2 font-medium tracking-normal text-black"
                  : "font-semibold leading-snug tracking-tight",
                "mt-2 dark:text-white",
              )}
            >
              <Link href={`/blog/${post.slug}`}>
                <span
                  className="bg-gradient-to-r from-green-200 to-green-100 bg-[length:0px_10px] bg-left-bottom
                  bg-no-repeat transition-[background-size] duration-500
                  hover:bg-[length:100%_3px] group-hover:bg-[length:100%_10px]
                  dark:from-primary/30 dark:to-primary/30"
                >
                  {post.title}
                </span>
              </Link>
            </h2>
            <div className="mt-3 flex items-center space-x-3 text-gray-500 dark:text-gray-400">
              <time className="truncate text-sm" dateTime={post.publishedAt || post.createdAt}>
                {post.publishedAt || post.createdAt
                  ? format(parseISO(post.publishedAt || post.createdAt), "dd MMMM, yyyy", { locale: vi })
                  : "Ngày không xác định"}
              </time>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}