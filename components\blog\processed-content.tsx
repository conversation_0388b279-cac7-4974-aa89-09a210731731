"use client"

import { useEffect, useRef } from "react"
import slugify from "slugify"

interface ProcessedContentProps {
  content: string
  className?: string
}
export function addIdsToHeadings(): void {
  if (typeof document === "undefined") return

  const headings = document.querySelectorAll(".blog-content h2, .blog-content h3, .blog-content h4")

  headings.forEach((heading) => {
    if (!heading.id && heading.textContent) {
      heading.id = slugify(heading.textContent)
    }
  })
}
export default function ProcessedContent({ content, className = "" }: ProcessedContentProps) {
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    addIdsToHeadings()
  }, [content])
  return (
    <div
      ref={contentRef}
      className={`prose prose-lg dark:prose-invert max-w-none blog-content ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

