import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";

interface BreadcrumbCustomProps {
  nameFirst?: string;
  nameSecond?: string;
  linkSecond?: string;
  nameThird?: string;
  linkThird?: string;
  nameFourth?: string;
}

export default function BreadcrumbUI({
  nameSecond,
  linkSecond,
  nameThird,
  linkThird,
  nameFourth,
}: BreadcrumbCustomProps) {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/vi">Trang chủ</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        {nameSecond && linkSecond && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href={linkSecond}>{nameSecond}</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
          </>
        )}

        {nameThird && linkThird && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href={linkThird}>{nameThird}</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
          </>
        )}

        {nameFourth && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{nameFourth}</BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
