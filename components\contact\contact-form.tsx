'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface FormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  urgency: 'low' | 'medium' | 'high';
  preferredContact: 'email' | 'phone' | 'chat';
}

export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    urgency: 'medium',
    preferredContact: 'email'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="text-center p-12 rounded-3xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border border-green-200 dark:border-green-800/30">
          <div className="text-6xl mb-6 animate-bounce">🎉</div>
          <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            Cảm ơn bạn đã liên hệ!
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Chúng tôi đã nhận được tin nhắn của bạn và sẽ phản hồi trong vòng 24 giờ.
            Nếu cần hỗ trợ khẩn cấp, vui lòng gọi hotline của chúng tôi.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => {
                setIsSubmitted(false);
                setFormData({
                  name: '',
                  email: '',
                  phone: '',
                  subject: '',
                  message: '',
                  urgency: 'medium',
                  preferredContact: 'email'
                });
              }}
              className="px-6 py-3 bg-white text-green-600 rounded-xl font-semibold border border-green-200 hover:bg-green-50 transition-all duration-300"
            >
              Gửi tin nhắn khác
            </button>
            <a
              href="/quiz"
              className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300"
            >
              Thử trắc nghiệm tâm lý
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
            <CardHeader className="pb-8">
              <CardTitle className="text-2xl md:text-3xl font-bold text-[#375E5E]">
                Liên hệ với chúng tôi
              </CardTitle>
              <p className="text-gray-700">
                Mọi thông tin đều được bảo mật tuyệt đối. Chúng tôi sẽ lắng nghe và hỗ trợ bạn một cách chuyên nghiệp nhất.
              </p>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name & Email */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Họ và tên *
                    </Label>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                      placeholder="Nhập họ và tên của bạn"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email *
                    </Label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Phone & Subject */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-sm font-medium">
                      Số điện thoại
                    </Label>
                    <input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                      placeholder="0123456789 (không bắt buộc)"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-sm font-medium">
                      Chủ đề *
                    </Label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Chọn chủ đề...</option>
                      <option value="consultation">Tư vấn tâm lý</option>
                      <option value="therapy">Liệu pháp tâm lý</option>
                      <option value="assessment">Đánh giá tâm lý</option>
                      <option value="group">Nhóm hỗ trợ</option>
                      <option value="emergency">Hỗ trợ khẩn cấp</option>
                      <option value="other">Khác</option>
                    </select>
                  </div>
                </div>

                {/* Urgency & Preferred Contact */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="urgency" className="text-sm font-medium">
                      Mức độ khẩn cấp
                    </Label>
                    <select
                      id="urgency"
                      name="urgency"
                      value={formData.urgency}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                    >
                      <option value="low">Không khẩn cấp</option>
                      <option value="medium">Bình thường</option>
                      <option value="high">Khẩn cấp</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preferredContact" className="text-sm font-medium">
                      Cách liên hệ ưa thích
                    </Label>
                    <select
                      id="preferredContact"
                      name="preferredContact"
                      value={formData.preferredContact}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300"
                    >
                      <option value="email">Email</option>
                      <option value="phone">Điện thoại</option>
                      <option value="chat">Chat (Zalo/Messenger)</option>
                    </select>
                  </div>
                </div>

                {/* Message */}
                <div className="space-y-2">
                  <Label htmlFor="message" className="text-sm font-medium">
                    Nội dung tin nhắn *
                  </Label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-white focus:ring-2 focus:ring-[#375E5E] focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Vui lòng chia sẻ những gì bạn đang quan tâm hoặc cần hỗ trợ. Chúng tôi sẽ lắng nghe và tư vấn một cách chuyên nghiệp."
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                  >
                    <span className="relative z-10">
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Đang gửi...
                        </>
                      ) : (
                        'Gửi tin nhắn'
                      )}
                    </span>
                    <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Contact */}
          <Card className="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20">
            <CardContent className="p-6">
              <h3 className="text-lg font-bold mb-4 text-blue-600 dark:text-blue-400">
                ⚡ Liên hệ nhanh
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-xl bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                    <span className="text-blue-600 dark:text-blue-400">📞</span>
                  </div>
                  <div>
                    <p className="font-medium text-sm">Hotline 24/7</p>
                    <p className="text-xs text-muted-foreground">1900-xxxx</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-xl bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                    <span className="text-green-600 dark:text-green-400">💬</span>
                  </div>
                  <div>
                    <p className="font-medium text-sm">Chat Zalo</p>
                    <p className="text-xs text-muted-foreground">Phản hồi trong 30 phút</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20">
            <CardContent className="p-6">
              <h3 className="text-lg font-bold mb-4 text-purple-600 dark:text-purple-400">
                💡 Tips khi liên hệ
              </h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                  <span className="text-purple-500 mt-0.5">✨</span>
                  <span>Hãy mô tả cảm xúc của bạn một cách tự nhiên nhất</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-purple-500 mt-0.5">🔒</span>
                  <span>Mọi thông tin đều được bảo mật tuyệt đối</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-purple-500 mt-0.5">💜</span>
                  <span>Chúng mình không phán xét, chỉ lắng nghe và hỗ trợ</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-purple-500 mt-0.5">⚡</span>
                  <span>Nếu khẩn cấp, hãy gọi hotline ngay</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
