"use client"

import Image from "next/image"
import Link from "next/link"

interface Expert {
  id: number
  name: string
  title: string
  specialties: string[]
  experience: string
  education: string
  description: string
  image: string
  languages: string[]
  consultationTypes: string[]
  rating: number
  reviewCount: number
  isAvailable: boolean
}

interface ExpertCardProps {
  expert: Expert
}

export default function ExpertCard({ expert }: ExpertCardProps) {
  return (
    <div className={`group relative p-6 rounded-3xl bg-white border transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 ${
      expert.isAvailable 
        ? 'border-gray-200 hover:border-[#375E5E]' 
        : 'border-gray-300 opacity-75'
    }`}>
      {/* Background overlay */}
      <div className="absolute inset-0 bg-[#375E5E]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      
      {/* Availability indicator */}
      <div className="absolute top-4 right-4 z-20">
        <div className={`w-3 h-3 rounded-full ${
          expert.isAvailable ? 'bg-green-500' : 'bg-gray-400'
        }`}></div>
      </div>

      <div className="relative z-10">
        {/* Expert Image */}
        <div className="relative w-24 h-24 mx-auto mb-6 rounded-2xl overflow-hidden bg-gray-100">
          <div className="w-full h-full bg-gradient-to-br from-[#375E5E]/20 to-[#375E5E]/40 flex items-center justify-center">
            <svg className="w-12 h-12 text-[#375E5E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>

        {/* Expert Info */}
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-black mb-2">{expert.name}</h3>
          <p className="text-[#375E5E] font-semibold mb-2">{expert.title}</p>
          <p className="text-sm text-gray-600 mb-3">{expert.experience}</p>
          
          {/* Rating */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(expert.rating) ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span className="text-sm text-gray-600">
              {expert.rating} ({expert.reviewCount} đánh giá)
            </span>
          </div>
        </div>

        {/* Specialties */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-black mb-3">Chuyên môn:</h4>
          <div className="flex flex-wrap gap-2">
            {expert.specialties.slice(0, 3).map((specialty, index) => (
              <span
                key={index}
                className="px-3 py-1 text-xs font-medium bg-[#375E5E]/10 text-[#375E5E] rounded-full"
              >
                {specialty}
              </span>
            ))}
            {expert.specialties.length > 3 && (
              <span className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                +{expert.specialties.length - 3} khác
              </span>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-sm text-gray-700 leading-relaxed mb-6 line-clamp-3">
          {expert.description}
        </p>

        {/* Consultation Types */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-black mb-3">Hình thức tư vấn:</h4>
          <div className="flex flex-wrap gap-2">
            {expert.consultationTypes.map((type, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-lg"
              >
                {type}
              </span>
            ))}
          </div>
        </div>

        {/* Languages */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-black mb-3">Ngôn ngữ:</h4>
          <div className="flex flex-wrap gap-2">
            {expert.languages.map((language, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-lg"
              >
                {language}
              </span>
            ))}
          </div>
        </div>

        {/* Education */}
        <div className="mb-6 p-3 bg-gray-50 rounded-xl">
          <h4 className="text-sm font-semibold text-black mb-2">Học vấn:</h4>
          <p className="text-xs text-gray-700">{expert.education}</p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          {expert.isAvailable ? (
            <>
              <Link
                href={`/contact?expert=${expert.id}`}
                className="w-full group relative inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <span className="relative z-10">Đặt lịch tư vấn</span>
                <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Link>
              <Link
                href={`/experts/${expert.id}`}
                className="w-full inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-black bg-white border border-gray-300 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#375E5E]"
              >
                Xem chi tiết
              </Link>
            </>
          ) : (
            <div className="w-full inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-gray-500 bg-gray-100 rounded-2xl cursor-not-allowed">
              Hiện không có lịch
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
