"use client";
import { ArrowUp } from "lucide-react";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";

export default function FloatingButtons() {
    const [showScrollTop, setShowScrollTop] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setShowScrollTop(window.scrollY > 200);
        };
        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    return (
        <div className="fixed bottom-4 right-4 z-50 flex flex-col space-y-4">
            {showScrollTop && (
                <motion.button
                    onClick={scrollToTop}
                    whileHover={{ scale: 1.1 }}
                    className="flex items-center justify-center rounded-full text-white w-12 h-12 transition-transform hover:shadow-md duration-200 ease-in-out"
                >
                    <ArrowUp className="h-6 w-6" />
                </motion.button>
            )}
        </div>
    );
}