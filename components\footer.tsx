import Link from 'next/link'

export default function Footer() {
    return (
        <footer className="w-full bg-white border-t border-gray-200">
            <div className="container flex flex-col gap-12 px-4 py-16 md:px-6 lg:py-20">
                <div className="grid gap-12 sm:grid-cols-2 md:grid-cols-4">
                    <div className="space-y-6">
                        <div className="flex items-center gap-3">
                            <div className="w-12 h-12 rounded-xl bg-[#375E5E] flex items-center justify-center">
                                <span className="text-white font-bold text-xl">A</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="font-bold text-xl text-[#375E5E]">
                                    Anmy
                                </span>
                                <span className="text-sm text-gray-600 -mt-1">Psychology</span>
                            </div>
                        </div>
                        <p className="text-sm text-gray-700 leading-relaxed">
                            Trung tâm tâm lý chuyên nghiệp Anmy cung cấp dịch vụ tư vấn và điều trị tâm lý
                            với đội ngũ chuyên gia giàu kinh nghiệm và phương pháp hiện đại.
                        </p>
                        <div className="flex gap-3">
                            <Link
                                href="#"
                                className="p-3 rounded-xl bg-[#375E5E]/10 text-[#375E5E] hover:bg-[#375E5E]/20 transition-all duration-300 hover:scale-110"
                            >
                                <svg className="size-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                                <span className="sr-only">Twitter</span>
                            </Link>
                            <Link
                                href="#"
                                className="p-3 rounded-xl bg-[#375E5E]/10 text-[#375E5E] hover:bg-[#375E5E]/20 transition-all duration-300 hover:scale-110"
                            >
                                <svg className="size-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                                </svg>
                                <span className="sr-only">Pinterest</span>
                            </Link>
                            <Link
                                href="#"
                                className="p-3 rounded-xl bg-[#375E5E]/10 text-[#375E5E] hover:bg-[#375E5E]/20 transition-all duration-300 hover:scale-110"
                            >
                                <svg className="size-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                                </svg>
                                <span className="sr-only">TikTok</span>
                            </Link>
                            <Link
                                href="#"
                                className="p-3 rounded-xl bg-[#375E5E] text-white hover:bg-[#375E5E]/90 transition-all duration-300 hover:scale-110"
                            >
                                <svg className="size-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                </svg>
                                <span className="sr-only">Instagram</span>
                            </Link>
                        </div>
                    </div>
                    <div className="space-y-6">
                        <h4 className="text-lg font-bold text-[#375E5E]">
                            Dịch vụ
                        </h4>
                        <ul className="space-y-3 text-sm">
                            <li>
                                <Link
                                    href="/services/counseling"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Tư vấn cá nhân
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/services/therapy"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Liệu pháp tâm lý
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/quiz"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Trắc nghiệm tâm lý
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/services/group-therapy"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Liệu pháp nhóm
                                </Link>
                            </li>
                        </ul>
                    </div>
                    <div className="space-y-6">
                        <h4 className="text-lg font-bold text-[#375E5E]">
                            Tài nguyên
                        </h4>
                        <ul className="space-y-3 text-sm">
                            <li>
                                <Link
                                    href="/blog"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Blog tâm lý
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/resources"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Tài liệu học tập
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/faq"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    FAQ
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/support"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Hỗ trợ khẩn cấp
                                </Link>
                            </li>
                        </ul>
                    </div>
                    <div className="space-y-6">
                        <h4 className="text-lg font-bold text-[#375E5E]">
                            Về Anmy
                        </h4>
                        <ul className="space-y-3 text-sm">
                            <li>
                                <Link
                                    href="/about"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Câu chuyện của chúng tôi
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/team"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Đội ngũ chuyên gia
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/contact"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Liên hệ
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/privacy"
                                    className="flex items-center gap-2 text-gray-600 hover:text-[#375E5E] transition-colors duration-300"
                                >
                                    Chính sách bảo mật
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
                {/* CTA Section */}
                <div className="col-span-full">
                    <div className="p-8 rounded-3xl bg-[#375E5E] text-white text-center">
                        <h3 className="text-2xl font-bold mb-4">Sẵn sàng bắt đầu hành trình chăm sóc tâm lý?</h3>
                        <p className="mb-6 opacity-90">Đừng để những khó khăn tâm lý cản trở bạn. Hãy liên hệ với chúng tôi để được hỗ trợ chuyên nghiệp.</p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link
                                href="/contact"
                                className="px-8 py-3 bg-white text-[#375E5E] rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-105"
                            >
                                Liên hệ ngay
                            </Link>
                            <Link
                                href="/quiz"
                                className="px-8 py-3 bg-white/20 backdrop-blur-sm text-white rounded-xl font-semibold hover:bg-white/30 transition-all duration-300 hover:scale-105"
                            >
                                Trắc nghiệm miễn phí
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="col-span-full flex flex-col gap-6 sm:flex-row justify-between items-center border-t border-gray-200 pt-8">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-lg bg-[#375E5E] flex items-center justify-center">
                            <span className="text-white font-bold text-sm">A</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            &copy; {new Date().getFullYear()} <span className="font-semibold text-[#375E5E]">Anmy Psychology</span>. Dịch vụ tâm lý chuyên nghiệp
                        </p>
                    </div>
                    <div className="flex gap-6 text-xs text-gray-600">
                        <Link href="/privacy" className="hover:text-[#375E5E] transition-colors">
                            Privacy
                        </Link>
                        <Link href="/terms" className="hover:text-[#375E5E] transition-colors">
                            Terms
                        </Link>
                        <Link href="/cookies" className="hover:text-[#375E5E] transition-colors">
                            Cookies
                        </Link>
                    </div>
                </div>
            </div>
        </footer>
    )
}
