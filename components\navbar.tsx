"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
    Check,
    ChevronRight,
    Menu,
    X,
    Moon,
    Sun,
    ArrowRight,
    Star,
    Zap,
    Shield,
    Users,
    Bar<PERSON>hart,
    Layers,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTheme } from "next-themes"

export default function NavBar() {
    const [isScrolled, setIsScrolled] = useState(false)
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
    const { theme, setTheme } = useTheme()
    const [mounted, setMounted] = useState(false)
    const [blogPosts, setBlogPosts] = useState([])
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        setMounted(true)
        const handleScroll = () => {
            if (window.scrollY > 10) {
                setIsScrolled(true)
            } else {
                setIsScrolled(false)
            }
        }

        window.addEventListener("scroll", handleScroll)
        return () => window.removeEventListener("scroll", handleScroll)
    }, [])

    const toggleTheme = () => {
        setTheme(theme === "dark" ? "light" : "dark")
    }

    return (
        <header
            className={`sticky top-0 z-50 w-full backdrop-blur-xl transition-all duration-500 ${
                isScrolled
                    ? "bg-white/80 dark:bg-gray-900/80 shadow-lg border-b border-gray-200/20 dark:border-gray-700/20"
                    : "bg-transparent"
            }`}
        >
            <div className="container flex h-20 items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-xl bg-[#375E5E] flex items-center justify-center">
                        <span className="text-white font-bold text-lg">A</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="font-bold text-lg text-[#375E5E]">
                            Anmy
                        </span>
                        <span className="text-xs text-gray-600 -mt-1">Psychology</span>
                    </div>
                </div>
                <nav className="hidden md:flex gap-1">
                    <Link
                        href="/"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        Trang chủ
                    </Link>
                    <Link
                        href="/services"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        Dịch vụ
                    </Link>
                    <Link
                        href="/quiz"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        Trắc nghiệm
                    </Link>
                    <Link
                        href="/blog"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        Bài viết
                    </Link>
                    <Link
                        href="/faq"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        FAQ
                    </Link>
                    <Link
                        href="/contact"
                        className="px-4 py-2 text-sm font-medium text-black rounded-xl transition-all duration-300 hover:bg-[#375E5E]/10 hover:text-[#375E5E]"
                    >
                        Liên hệ
                    </Link>
                </nav>
                <div className="hidden md:flex gap-3 items-center">
                    <button
                        onClick={toggleTheme}
                        className="p-2 rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300 hover:scale-110"
                    >
                        {mounted && theme === "dark" ?
                            <Sun className="size-[18px] text-yellow-500" /> :
                            <Moon className="size-[18px] text-purple-600" />
                        }
                        <span className="sr-only">Chuyển đổi giao diện sáng/tối</span>
                    </button>

                    <Link
                        href="/contact"
                        className="px-6 py-2 text-sm font-semibold text-white bg-[#375E5E] rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-[#375E5E]/90"
                    >
                        Đặt lịch
                    </Link>
                </div>
                <div className="flex items-center gap-2 md:hidden">
                    <button
                        onClick={toggleTheme}
                        className="p-2 rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300"
                    >
                        {mounted && theme === "dark" ?
                            <Sun className="size-[18px] text-yellow-500" /> :
                            <Moon className="size-[18px] text-purple-600" />
                        }
                    </button>
                    <button
                        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        className="p-2 rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300"
                    >
                        {mobileMenuOpen ? <X className="size-5" /> : <Menu className="size-5" />}
                        <span className="sr-only">Mở menu</span>
                    </button>
                </div>
            </div>

            {mobileMenuOpen && (
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="md:hidden absolute top-20 inset-x-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-b border-gray-200/20 dark:border-gray-700/20 shadow-xl"
                >
                    <div className="container py-6 flex flex-col gap-2">
                        <Link
                            href="/"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            Trang chủ
                        </Link>
                        <Link
                            href="/services"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            Dịch vụ
                        </Link>
                        <Link
                            href="/quiz"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            Trắc nghiệm
                        </Link>
                        <Link
                            href="/blog"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            Bài viết
                        </Link>
                        <Link
                            href="/faq"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            FAQ
                        </Link>
                        <Link
                            href="/contact"
                            className="flex items-center gap-3 py-3 px-4 text-sm font-medium text-black rounded-xl hover:bg-[#375E5E]/10 hover:text-[#375E5E] transition-all duration-300"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            Liên hệ
                        </Link>

                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <Link
                                href="/contact"
                                className="flex items-center justify-center gap-2 py-3 px-4 text-sm font-semibold text-white bg-[#375E5E] rounded-xl shadow-lg transition-all duration-300 hover:bg-[#375E5E]/90"
                                onClick={() => setMobileMenuOpen(false)}
                            >
                                Đặt lịch tư vấn
                            </Link>
                        </div>
                    </div>
                </motion.div>
            )}
        </header>
    )
}
