'use client';

import { useState, useEffect } from 'react';
import { QuizQuestion, getRandomQuestions, calculateResult } from '@/data/quiz-questions';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface QuizState {
  questions: QuizQuestion[];
  currentQuestionIndex: number;
  answers: { [key: number]: number };
  isCompleted: boolean;
  totalScore: number;
  maxScore: number;
}

export default function QuizComponent() {
  const [quizState, setQuizState] = useState<QuizState>({
    questions: [],
    currentQuestionIndex: 0,
    answers: {},
    isCompleted: false,
    totalScore: 0,
    maxScore: 0
  });

  const [selectedAnswer, setSelectedAnswer] = useState<string>('');

  // Initialize quiz
  useEffect(() => {
    startNewQuiz();
  }, []);

  const startNewQuiz = () => {
    const randomQuestions = getRandomQuestions(20);
    const maxScore = randomQuestions.length * 3; // Max score per question is 3
    
    setQuizState({
      questions: randomQuestions,
      currentQuestionIndex: 0,
      answers: {},
      isCompleted: false,
      totalScore: 0,
      maxScore
    });
    setSelectedAnswer('');
  };

  const handleAnswerSelect = (value: string) => {
    setSelectedAnswer(value);
  };

  const handleNextQuestion = () => {
    if (!selectedAnswer) return;

    const currentQuestion = quizState.questions[quizState.currentQuestionIndex];
    const selectedOption = currentQuestion.options[parseInt(selectedAnswer)];
    
    const newAnswers = {
      ...quizState.answers,
      [currentQuestion.id]: selectedOption.score
    };

    if (quizState.currentQuestionIndex < quizState.questions.length - 1) {
      // Move to next question
      setQuizState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1,
        answers: newAnswers
      }));
      setSelectedAnswer('');
    } else {
      // Complete quiz
      const totalScore = Object.values(newAnswers).reduce((sum, score) => sum + score, 0);
      setQuizState(prev => ({
        ...prev,
        answers: newAnswers,
        totalScore,
        isCompleted: true
      }));
    }
  };

  const handlePreviousQuestion = () => {
    if (quizState.currentQuestionIndex > 0) {
      setQuizState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex - 1
      }));
      
      // Set the previously selected answer
      const prevQuestion = quizState.questions[quizState.currentQuestionIndex - 1];
      const prevAnswer = quizState.answers[prevQuestion.id];
      if (prevAnswer !== undefined) {
        const optionIndex = prevQuestion.options.findIndex(opt => opt.score === prevAnswer);
        setSelectedAnswer(optionIndex.toString());
      } else {
        setSelectedAnswer('');
      }
    }
  };

  if (quizState.questions.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Đang tải câu hỏi...</p>
        </div>
      </div>
    );
  }

  if (quizState.isCompleted) {
    const result = calculateResult(quizState.totalScore, quizState.maxScore);

    return (
      <div className="max-w-3xl mx-auto space-y-8">
        {/* Celebration Animation */}
        <div className="text-center">
          <div className="text-6xl mb-4 animate-bounce">✅</div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#375E5E] mb-2">
            Hoàn thành trắc nghiệm
          </h2>
          <p className="text-lg text-gray-700">Cảm ơn bạn đã dành thời gian để đánh giá sức khỏe tinh thần của mình</p>
        </div>

        {/* Result Card */}
        <div className="relative overflow-hidden rounded-3xl bg-white border border-gray-200 shadow-2xl">
          <div className="absolute inset-0 bg-[#375E5E]/5"></div>

          <div className="relative z-10 p-8 md:p-12">
            {/* Score Display */}
            <div className="text-center mb-8">
              <div className={`inline-flex items-center px-6 py-3 rounded-2xl text-xl font-bold ${result.bgColor} ${result.color} mb-4`}>
                <span className="mr-2">
                  {result.level === 'Tốt' && '✓'}
                  {result.level === 'Bình thường' && '○'}
                  {result.level === 'Cần chú ý' && '△'}
                  {result.level === 'Cần hỗ trợ' && '!'}
                </span>
                {result.level}
              </div>

              <div className="space-y-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {quizState.totalScore}/{quizState.maxScore} điểm
                </p>
                <p className="text-lg text-muted-foreground">
                  ({Math.round((quizState.totalScore / quizState.maxScore) * 100)}% - {result.level})
                </p>
              </div>
            </div>

            {/* Description */}
            <div className={`p-6 rounded-2xl ${result.bgColor} mb-8`}>
              <p className="text-center leading-relaxed font-medium">{result.description}</p>
            </div>

            {/* Recommendations */}
            <div className="mb-8">
              <h3 className="text-xl font-bold mb-4 text-center">Khuyến nghị:</h3>
              <div className="grid gap-3 md:grid-cols-2">
                {result.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50">
                    <span className="text-blue-500 text-lg mt-0.5">•</span>
                    <span className="text-sm leading-relaxed">{rec}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="text-center space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={startNewQuiz}
                  className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <span className="relative z-10">Làm lại trắc nghiệm</span>
                  <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <a
                  href="/contact"
                  className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-white backdrop-blur-sm rounded-2xl border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#375E5E]"
                >
                  Liên hệ tư vấn
                </a>
              </div>

              <p className="text-sm text-gray-600 max-w-md mx-auto leading-relaxed">
                <span className="font-medium">Lưu ý:</span> Kết quả này chỉ mang tính tham khảo.
                Nếu bạn cần hỗ trợ chuyên nghiệp, vui lòng liên hệ với chúng tôi.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const currentQuestion = quizState.questions[quizState.currentQuestionIndex];
  const progress = ((quizState.currentQuestionIndex + 1) / quizState.questions.length) * 100;

  return (
    <div className="max-w-3xl mx-auto space-y-8">
      {/* Progress Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="text-sm font-medium text-muted-foreground">
            Câu hỏi {quizState.currentQuestionIndex + 1} / {quizState.questions.length}
          </div>
          <div className="text-sm font-bold text-purple-600">
            {Math.round(progress)}% hoàn thành
          </div>
        </div>

        {/* Custom Progress Bar */}
        <div className="relative h-3 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="absolute top-0 left-0 h-full bg-[#375E5E] rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
        </div>
      </div>

      {/* Question Card */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border border-gray-200 dark:border-gray-700 shadow-xl">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-blue-500/5"></div>

        <div className="relative z-10 p-8 md:p-12">
          {/* Question */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#375E5E]/10 text-sm font-medium text-[#375E5E] mb-6">
              {currentQuestion.category === 'stress' && 'Căng thẳng'}
              {currentQuestion.category === 'anxiety' && 'Lo âu'}
              {currentQuestion.category === 'depression' && 'Tâm trạng'}
              {currentQuestion.category === 'self-esteem' && 'Tự tin'}
              {currentQuestion.category === 'social' && 'Xã hội'}
            </div>

            <h3 className="text-xl md:text-2xl font-bold leading-relaxed text-gray-900 dark:text-white mb-2">
              {currentQuestion.question}
            </h3>

            <p className="text-gray-700">Vui lòng chọn đáp án phù hợp nhất với tình trạng của bạn</p>
          </div>

          {/* Options */}
          <RadioGroup value={selectedAnswer} onValueChange={handleAnswerSelect} className="space-y-4">
            {currentQuestion.options.map((option, index) => (
              <div
                key={index}
                className={`group relative p-4 rounded-2xl border-2 transition-all duration-300 cursor-pointer hover:shadow-lg ${
                  selectedAnswer === index.toString()
                    ? 'border-purple-500 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 shadow-lg'
                    : 'border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 bg-white dark:bg-gray-800/50'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <RadioGroupItem
                    value={index.toString()}
                    id={`option-${index}`}
                    className="text-purple-600"
                  />
                  <Label
                    htmlFor={`option-${index}`}
                    className="flex-1 cursor-pointer text-base leading-relaxed font-medium"
                  >
                    {option.text}
                  </Label>

                  {selectedAnswer === index.toString() && (
                    <div className="text-[#375E5E] text-xl">✓</div>
                  )}
                </div>
              </div>
            ))}
          </RadioGroup>

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center pt-8 mt-8 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handlePreviousQuestion}
              disabled={quizState.currentQuestionIndex === 0}
              className="inline-flex items-center px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-2xl border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
            >
              ← Câu trước
            </button>

            <button
              onClick={handleNextQuestion}
              disabled={!selectedAnswer}
              className="group relative inline-flex items-center px-8 py-3 text-base font-semibold text-white bg-[#375E5E] rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
            >
              <span className="relative z-10">
                {quizState.currentQuestionIndex === quizState.questions.length - 1 ? 'Xem kết quả' : 'Tiếp theo →'}
              </span>
              <div className="absolute inset-0 bg-[#375E5E]/90 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>
      </div>

      {/* Quiz Info */}
      <div className="text-center space-y-2">
        <p className="text-gray-700">
          Trắc nghiệm đánh giá sức khỏe tinh thần
        </p>
        <p className="text-sm text-gray-600">
          Thời gian: ~5 phút • Hoàn toàn miễn phí • Kết quả ngay lập tức
        </p>
      </div>
    </div>
  );
}
