'use client';
import {
  EmailIcon,
  EmailShareButton,
  FacebookIcon,
  FacebookShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  PinterestIcon,
  PinterestShareButton,
  TwitterIcon,
  TwitterShareButton,
  LinkedinIcon,
  LinkedinShareButton,
} from "react-share";

interface ShareButtonsProps {
  shareUrl: string;
}

export default function ShareButtons({ shareUrl }: ShareButtonsProps) {
  return (
    <div className="flex justify-center items-center mt-2 space-x-2">
      <FacebookShareButton url={shareUrl} aria-label="Chia sẻ qua Facebook">
        <FacebookIcon size={40} round />
      </FacebookShareButton>
      
      <EmailShareButton url={shareUrl} aria-label="Chia sẻ qua Email">
        <EmailIcon size={40} round />
      </EmailShareButton>
      
      <WhatsappShareButton url={shareUrl} aria-label="Chia sẻ qua WhatsApp">
        <WhatsappIcon size={40} round />
      </WhatsappShareButton>
      
      <PinterestShareButton url={shareUrl} media={shareUrl} aria-label="Chia sẻ qua Pinterest">
        <PinterestIcon size={40} round />
      </PinterestShareButton>
      
      <TwitterShareButton url={shareUrl} aria-label="Chia sẻ qua Twitter">
        <TwitterIcon size={40} round />
      </TwitterShareButton>
      
      <LinkedinShareButton url={shareUrl} aria-label="Chia sẻ qua LinkedIn">
        <LinkedinIcon size={40} round />
      </LinkedinShareButton>
    </div>
  );
}
