export interface WordPressPost {
  id: number;
  slug: string;
  date: string;
  title: { rendered: string };
  content: { rendered: string };
  excerpt: { rendered: string };
  featured_media: number;
  categories: number[];
  tags: number[];
  _embedded?: {
    "wp:featuredmedia"?: Array<{ source_url: string }>;
    author?: Array<{ name: string }>;
  };
}

export async function getBlogPosts(limit?: number) {
  try {
    let apiUrl = `${process.env.WORDPRESS_API_URL}/wp-json/wp/v2/posts?_embed`;
    
    if (limit) {
      apiUrl += `&per_page=${limit}`;
    }
    
    const res = await fetch(
      apiUrl,
      {
        next: { revalidate: 3600 }, 
      }
    );

    if (!res.ok) throw new Error("Lỗi khi fetch bài viết");

    const posts: WordPressPost[] = await res.json();
    return convertToBlogPostFormat(posts);
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    return [];
  }
}

export async function getPostDetail(slug: string): Promise<WordPressPost | null> {
  try {
    const res = await fetch(
      `${process.env.WORDPRESS_API_URL}/wp-json/wp/v2/posts?slug=${slug}&_embed`,
      {
        next: { revalidate: 3600 }, 
      }
    );

    if (!res.ok) throw new Error("Lỗi khi fetch bài viết");

    const data: WordPressPost[] = await res.json();
    return data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error("Lỗi khi lấy chi tiết bài viết:", error);
    return null;
  }
}


export const convertToBlogPostFormat = (wpPosts: WordPressPost[]) => {
  return wpPosts.map(post => ({
    id: post.id.toString(),
    title: post.title.rendered,
    slug: post.slug,
    excerpt: post.excerpt.rendered,
    content: post.content.rendered,
    coverImage: post._embedded?.["wp:featuredmedia"]?.[0]?.source_url || "/logo.webp",
    tags: post.tags.map(tag => tag.toString()),
    category: post.categories[0]?.toString() || "",
    publishedAt: post.date,
    createdAt: post.date,
  }));
};
